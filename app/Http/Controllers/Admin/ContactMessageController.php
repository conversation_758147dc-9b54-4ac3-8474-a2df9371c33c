<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Requests\MassDestroyContactMessageRequest;
use App\Http\Requests\StoreContactMessageRequest;
use App\Http\Requests\UpdateContactMessageRequest;
use App\Models\ContactMessage;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class ContactMessageController extends Controller
{
    use CsvImportTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('contact_message_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = ContactMessage::query()->select(sprintf('%s.*', (new ContactMessage)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'contact_message_show';
                $editGate      = 'contact_message_edit';
                $deleteGate    = 'contact_message_delete';
                $crudRoutePart = 'contact-messages';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                return $row->id ? $row->id : '';
            });
            $table->editColumn('name', function ($row) {
                return $row->name ? $row->name : '';
            });
            $table->editColumn('email', function ($row) {
                return $row->email ? $row->email : '';
            });
            $table->editColumn('message', function ($row) {
                return $row->message ? $row->message : '';
            });
            $table->editColumn('status', function ($row) {
                return $row->status ? ContactMessage::STATUS_SELECT[$row->status] : '';
            });

            $table->rawColumns(['actions', 'placeholder']);

            return $table->make(true);
        }

        return view('admin.contactMessages.index');
    }

    public function create()
    {
        abort_if(Gate::denies('contact_message_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.contactMessages.create');
    }

    public function store(StoreContactMessageRequest $request)
    {
        $contactMessage = ContactMessage::create($request->all());

        return redirect()->route('admin.contact-messages.index');
    }

    public function edit(ContactMessage $contactMessage)
    {
        abort_if(Gate::denies('contact_message_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.contactMessages.edit', compact('contactMessage'));
    }

    public function update(UpdateContactMessageRequest $request, ContactMessage $contactMessage)
    {
        $contactMessage->update($request->all());

        return redirect()->route('admin.contact-messages.index');
    }

    public function show(ContactMessage $contactMessage)
    {
        abort_if(Gate::denies('contact_message_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $contactMessage->status = 'read';
        $contactMessage->save();

        return view('admin.contactMessages.show', compact('contactMessage'));
    }

    public function destroy(ContactMessage $contactMessage)
    {
        abort_if(Gate::denies('contact_message_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $contactMessage->delete();

        return back();
    }

    public function massDestroy(MassDestroyContactMessageRequest $request)
    {
        $contactMessages = ContactMessage::find(request('ids'));

        foreach ($contactMessages as $contactMessage) {
            $contactMessage->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }
}

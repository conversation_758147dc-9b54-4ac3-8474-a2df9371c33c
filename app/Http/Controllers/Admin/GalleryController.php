<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\MediaUploadingTrait;
use App\Http\Requests\MassDestroyGalleryRequest;
use App\Http\Requests\StoreGalleryRequest;
use App\Http\Requests\UpdateGalleryRequest;
use App\Models\Gallery;
use App\Models\Page;
use Gate;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class GalleryController extends Controller
{
    use MediaUploadingTrait;


    public function index(Request $request, $id)
    {

        if ($request->ajax()) {

            $mediaQuery = Media::query();

            $search = $request->get('search');
            if (!empty($search)) {
                $mediaQuery->where('name', 'like', "%{$search}%");
            }

            $media = $mediaQuery->orderBy('id', 'desc')->paginate(8);

            $media->appends(['search' => $search]);

            return view('admin.galleries.list', compact('media', 'search'));
        }

        $mediaQuery = Media::query();

        $search = $request->get('search');
        if (!empty($search)) {
            $mediaQuery->where('name', 'like', "%{$search}%");
        }

        $media = $mediaQuery->orderBy('id', 'desc')->paginate(8);

        $media->appends(['search' => $search]);


        return view('admin.media.index', compact(
            'media',
            'search'
        ));
    }

    public function upload(Request $request, $id)
    {
        $request->validate([
            'file' => 'required|file|mimes:jpeg,jpg,png,gif,svg|max:20480'
        ]);

        $gallery = Page::findOrFail($id);

        $gallery->addMedia($request->file('file'))->toMediaCollection('photos');

        return response()->json([
            'status' => 'success',
            'message' => 'Image uploaded',
        ]);
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Models\ContactMessage;

class HomeController
{
    public function index()
    {
        $messages_read = ContactMessage::where('status', 'read')->count();
        $messages_unread = ContactMessage::where('status', 'unread')->count();
        $messages = ContactMessage::orderBy('created_at', 'desc')->take(10)->get();
        return view('home', compact('messages_read', 'messages_unread', 'messages'));
    }
}

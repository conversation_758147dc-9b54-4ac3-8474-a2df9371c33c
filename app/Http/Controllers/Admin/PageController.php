<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Requests\MassDestroyPageRequest;
use App\Http\Requests\StorePageRequest;
use App\Http\Requests\UpdatePageRequest;
use App\Models\Page;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

use App\Models\Section;
use App\Models\SectionItem;
use Illuminate\Support\Facades\Storage;
use App\Utils\SectionUtils;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PageController extends Controller
{
    use CsvImportTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('page_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = Page::query()->select(sprintf('%s.*', (new Page)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'page_show';
                $editGate      = 'page_edit';
                $deleteGate    = 'page_delete';
                $crudRoutePart = 'pages';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                return $row->id ? $row->id : '';
            });
            $table->editColumn('title', function ($row) {
                return $row->title ? $row->title : '';
            });
            $table->editColumn('slug', function ($row) {
                return $row->slug ? $row->slug : '';
            });
            $table->editColumn('content', function ($row) {

                return '<a class="btn btn-icon btn-active-light-primary w-150px h-30px" target="_blank" href="' . route('admin.editors.show', [$row->id, 'fr', 'header']) . '">
                    Content FR
                    <span>
                        <i class="fa-solid fa-pencil text-white ms-2"></i>
                    </span>
                </a>';
            });

            $table->rawColumns(['actions', 'placeholder', 'content']);

            return $table->make(true);
        }

        return view('admin.pages.index');
    }

    public function create()
    {
        abort_if(Gate::denies('page_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.pages.create');
    }

    public function store(StorePageRequest $request)
    {
        $page = Page::create($request->all());

        return redirect()->route('admin.pages.index');
    }

    public function edit(Page $page)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.pages.edit', compact('page'));
    }

    public function update(UpdatePageRequest $request, Page $page)
    {
        $page->update($request->all());
        return redirect()->route('admin.pages.index');
    }

    public function show(Page $page, $language = null, $section = null, Request $request)
    {
        abort_if(Gate::denies('page_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if (empty($language) || empty($section)) {
            return redirect()->route('pages.show.language', [
                'page' => $page->id,
                'language' => $language ?? 'fr',
                'section' => $section ?? 'header'
            ]);
        }

        if ($page->id == 1)
        {
           $section = 'index';
           $bg_url = asset('images/bg1.jpg');
        }
        else if ($page->id == 2) {
            $section = 'missions';
            $bg_url = asset('images/bg3.jpg');
        }
        else
        {
           $section = 'header';
        }

        $page->load('pageSeos');

        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, $section, $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);


        return view('admin.pages.show', compact(
            'page',
            'section_data',
            'section_header',
            'section_footer',
            'language',
            'section',
            'bg_url'
        ));
    }

    public function destroy(Page $page)
    {
        abort_if(Gate::denies('page_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $page->delete();

        return back();
    }

    public function massDestroy(MassDestroyPageRequest $request)
    {
        $pages = Page::find(request('ids'));

        foreach ($pages as $page) {
            $page->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }

    private function updateHeader (Request $request)
    {
        $section = Section::where('page_id', 1)->where('section_type', 'header')->first();

        if ($section) {
            // 1. Handle the Logo
            SectionItem::where('section_id', $section->id)->where('groups', 'logo')->delete();
            SectionItem::create([
                'section_id' => $section->id,
                'image' => $request->logo,
                'sort_order' => 0,
                'groups' => 'logo',
            ]);

            // 2. Handle the Menu Items
            SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'menu_list')->delete();
            $menu_list = json_decode($request->menu, true);

            foreach ($menu_list as $key => $value) {

                if ($value['title'] && $value['url']) {
                    SectionItem::create([
                        'section_id' => $section->id,
                        'title' => $value['title'],
                        'url' => $value['url'],
                        'sort_order' => $key,
                        'groups' => 'menu_list',
                        'language' => $request->language,
                    ]);
                }
            }
        }

    }

    private function updateFooter (Request $request)
    {
        $section = Section::where('page_id', 1)->where('section_type', 'footer')->first();

        if ($section) {

            SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'copyright')->delete();
            SectionItem::create([
                'section_id' => $section->id,
                'title' => $request->copyright,
                'sort_order' => 0,
                'groups' => 'copyright',
                'language' => $request->language,
            ]);
        }

    }

    public function drawerForm(Request $request)
    {
        $html = "";
        $language = $request->language ?? 'fr';

        if ($request->section == "header") {
            $section_header = SectionUtils::getSectionHeaderData($language);
            $html = view('admin.pages.sectionForm.header', compact('section_header', 'language'))->render();
        }

        if ($request->section == "footer") {
            $section_footer = SectionUtils::getSectionFooterData($language);
            $html = view('admin.pages.sectionForm.footer', compact('section_footer', 'language'))->render();
        }

        /* Page: Home Start */

        if ($request->section == "index") {
            $section_data = SectionUtils::getSectionData($language, $request->section, $request->page_id);
            $html = view('admin.pages.content.index', compact('section_data', 'language'))->render();
        }

        /* Page: Missions Start */
        if ($request->section == "missions") {
            $section_data = SectionUtils::getSectionData($language, $request->section, $request->page_id);
            $html = view('admin.pages.content.missions', compact('section_data', 'language'))->render();
        }
        /* Page: Missions End */

        /* Page: bureaux Start */
        if ($request->section == "bureaux") {
            $section_data = SectionUtils::getSectionData($language, $request->section, $request->page_id);
            $html = view('admin.pages.content.bureaux', compact('section_data', 'language'))->render();
        }
        /* Page: bureaux End */

        /* Page: introduction Start */
        if ($request->section == "introduction") {
            $section_data = SectionUtils::getSectionData($language, $request->section, $request->page_id);
            $html = view('admin.pages.content.introduction', compact('section_data', 'language'))->render();
        }
        /* Page: introduction End */

        /* Page: professionnels Start */
        if ($request->section == "professionnels") {
            $section_data = SectionUtils::getSectionData($language, $request->section, $request->page_id);
            $html = view('admin.pages.content.professionnels', compact('section_data', 'language'))->render();
        }
        /* Page: professionnels End */

        /* Page: assurances Start */
        if ($request->section == "assurances") {
            $section_data = SectionUtils::getSectionData($language, $request->section, $request->page_id);
            $html = view('admin.pages.content.assurances', compact('section_data', 'language'))->render();
        }
        /* Page: assurances End */

        /* Page: localisations Start */
        if ($request->section == "localisations") {
            $section_data = SectionUtils::getSectionData($language, $request->section, $request->page_id);
            $html = view('admin.pages.content.localisations', compact('section_data', 'language'))->render();
        }
        /* Page: localisations End */

        /* Page: comores Start */
        if ($request->section == "comores") {
            $section_data = SectionUtils::getSectionData($language, $request->section, $request->page_id);
            $html = view('admin.pages.content.comores', compact('section_data', 'language'))->render();
        }
        /* Page: comores End */

        return response()->json(['section_name' => $request->section, 'content' => $html]);
    }

    public function editor(int $pageId,?string $language = null,?string $section  = null,Request $request)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN);

        if (!Page::where('id', $pageId)->exists()) { return abort(404);}

        /* ---------------------------------------------
         | Page, defaults & redirects
         *---------------------------------------------*/
        $page            = Page::findOrFail($pageId);
        $bg_url          = asset('images/' . $page->bg);
        $admin_page      = $page->admin_page ?? $page->default_section;
        $default_section = $page->default_section;
        $update_url      = $page->update_route;

        if (empty($language) || empty($section)) {
            return redirect()->route('admin.editors.show', [
                'pageId'   => $pageId,
                'language' => $language ?? 'fr',
                'section'  => $default_section,
            ]);
        }

        /* ---------------------------------------------
         | Section data
         *---------------------------------------------*/
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $section_data   = SectionUtils::getSectionData($language,in_array($section, ['header', 'footer']) ? $default_section : $section,$page->id);
        $page->load('pageSeos');

        return view('admin.editors.view', compact(
            'page',
            'section_data',
            'section_header',
            'section_footer',
            'language',
            'section',
            'bg_url',
            'admin_page',
            'update_url'
        ));
    }

    public function editorIndexUpdate(UpdatePageRequest $request)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $page = Page::find(1);

        if ($request->section == 'header') {
            $this->updateHeader($request);
        }
        if ($request->section == 'footer') {
            $this->updateFooter($request);
        }
        if ($request->section == 'index') {

            $section = Section::where('page_id', $page->id)->where('section_type', 'index')->first();

            if ($section) {

                // 1. title_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_1,
                    'sort_order' => 0,
                    'groups' => 'title_1',
                    'language' => $request->language,
                ]);

                // 2. title_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_2,
                    'sort_order' => 0,
                    'groups' => 'title_2',
                    'language' => $request->language,
                ]);

                // 3. Handle the list_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_1')->delete();
                $list_1 = json_decode($request->list_1, true);

                if ($list_1 != null) {
                    foreach ($list_1 as $key => $value) {
                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_1',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 4. Handle the title_3

                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_3')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_3,
                    'sort_order' => 0,
                    'groups' => 'title_3',
                    'language' => $request->language,
                ]);

                // 5. Handle the list_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_2')->delete();
                $list_2 = json_decode($request->list_2, true);

                if ($list_2 != null) {
                    foreach ($list_2 as $key => $value) {
                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_2',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 6. Handle the list_3
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_3')->delete();
                $list_3 = json_decode($request->list_3, true);
                if ($list_3 != null) {
                    foreach ($list_3 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_3',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }
            }
        }

        return redirect()->route('admin.editors.show', [
            'pageId' => $page->id,
            'language' => $request->language,
            'section' => $request->section
        ]);

    }

    public function editorMissionsUpdate(UpdatePageRequest $request)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $page = Page::find(2);

        if ($request->section == 'header') {
            $this->updateHeader($request);
        }
        if ($request->section == 'footer') {
            $this->updateFooter($request);
        }
        if ($request->section == 'missions') {

            $section = Section::where('page_id', $page->id)->where('section_type', 'missions')->first();

            if ($section) {

                // 1. title_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_1,
                    'sort_order' => 0,
                    'groups' => 'title_1',
                    'language' => $request->language,
                ]);

                // 2. title_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_2,
                    'sort_order' => 0,
                    'groups' => 'title_2',
                    'language' => $request->language,
                ]);

                // 3. Handle the list_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_1')->delete();
                $list_1 = json_decode($request->list_1, true);

                if ($list_1 != null) {
                    foreach ($list_1 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_1',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 4. Handle the title_3

                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_3')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_3,
                    'sort_order' => 0,
                    'groups' => 'title_3',
                    'language' => $request->language,
                ]);

                // 5. Handle the list_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_2')->delete();
                $list_2 = json_decode($request->list_2, true);

                if ($list_2 != null) {
                    foreach ($list_2 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_2',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 6. Handle the list_3
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_3')->delete();
                $list_3 = json_decode($request->list_3, true);
                if ($list_3 != null) {
                    foreach ($list_3 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_3',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }
            }
        }

        return redirect()->route('admin.editors.show', [
            'pageId' => $page->id,
            'language' => $request->language,
            'section' => $request->section
        ]);

    }

    public function editorBureauxUpdate(UpdatePageRequest $request)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $page = Page::find(3);

        if ($request->section == 'header') {
            $this->updateHeader($request);
        }
        if ($request->section == 'footer') {
            $this->updateFooter($request);
        }
        if ($request->section == 'bureaux') {

            $section = Section::where('page_id', $page->id)->where('section_type', 'bureaux')->first();

            if ($section) {

                // 1. title_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_1,
                    'sort_order' => 0,
                    'groups' => 'title_1',
                    'language' => $request->language,
                ]);

                // 2. title_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_2,
                    'sort_order' => 0,
                    'groups' => 'title_2',
                    'language' => $request->language,
                ]);

                // 3. Handle the list_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_1')->delete();
                $list_1 = json_decode($request->list_1, true);

                if ($list_1 != null) {
                    foreach ($list_1 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_1',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 4. Handle the list_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_2')->delete();
                $list_2 = json_decode($request->list_2, true);

                if ($list_2 != null) {
                    foreach ($list_2 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_2',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 5. Handle the list_3
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_3')->delete();
                $list_3 = json_decode($request->list_3, true);
                if ($list_3 != null) {
                    foreach ($list_3 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_3',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }
            }
        }

        return redirect()->route('admin.editors.show', [
            'pageId' => $page->id,
            'language' => $request->language,
            'section' => $request->section
        ]);

    }

    public function editorIntroductionUpdate(UpdatePageRequest $request)
    {

        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $page = Page::find(4);

        if ($request->section == 'header') {
            $this->updateHeader($request);
        }
        if ($request->section == 'footer') {
            $this->updateFooter($request);
        }
        if ($request->section == 'introduction') {

            $section = Section::where('page_id', $page->id)->where('section_type', 'introduction')->first();

            if ($section) {

                // 1. title_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_1,
                    'sort_order' => 0,
                    'groups' => 'title_1',
                    'language' => $request->language,
                ]);

                // 2. description_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_1,
                    'sort_order' => 0,
                    'groups' => 'description_1',
                    'language' => $request->language,
                ]);

                // 3. description_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_2,
                    'sort_order' => 0,
                    'groups' => 'description_2',
                    'language' => $request->language,
                ]);

                // 4. description_3
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_3')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_3,
                    'sort_order' => 0,
                    'groups' => 'description_3',
                    'language' => $request->language,
                ]);

                // 5. description_4
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_4')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_4,
                    'sort_order' => 0,
                    'groups' => 'description_4',
                    'language' => $request->language,
                ]);

                // 6. Handle the list_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_1')->delete();
                $list_1 = json_decode($request->list_1, true);

                if ($list_1 != null) {
                    foreach ($list_1 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_1',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }


            }
        }

        return redirect()->route('admin.editors.show', [
            'pageId' => $page->id,
            'language' => $request->language,
            'section' => $request->section
        ]);

    }

    public function editorProfessionnelsUpdate(UpdatePageRequest $request)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $page = Page::find(5);

        if ($request->section == 'header') {
            $this->updateHeader($request);
        }
        if ($request->section == 'footer') {
            $this->updateFooter($request);
        }
        if ($request->section == 'professionnels') {
            $section = Section::where('page_id', $page->id)->where('section_type', 'professionnels')->first();

            if ($section) {

                // 1. title_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_1,
                    'sort_order' => 0,
                    'groups' => 'title_1',
                    'language' => $request->language,
                ]);

                // 2. description_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_1,
                    'sort_order' => 0,
                    'groups' => 'description_1',
                    'language' => $request->language,
                ]);

                // 3. description_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_2,
                    'sort_order' => 0,
                    'groups' => 'description_2',
                    'language' => $request->language,
                ]);

                // 4. description_3
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_3')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_3,
                    'sort_order' => 0,
                    'groups' => 'description_3',
                    'language' => $request->language,
                ]);

                // 5. title_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_2,
                    'sort_order' => 0,
                    'groups' => 'title_2',
                    'language' => $request->language,
                ]);

                // 6. description_4
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_4')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_4,
                    'sort_order' => 0,
                    'groups' => 'description_4',
                    'language' => $request->language,
                ]);
                // 7. description_5
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_5')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_5,
                    'sort_order' => 0,
                    'groups' => 'description_5',
                    'language' => $request->language,
                ]);

                // 8. Handle the list_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_1')->delete();
                $list_1 = json_decode($request->list_1, true);

                if ($list_1 != null) {
                    foreach ($list_1 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_1',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

            }
        }

        return redirect()->route('admin.editors.show', [
            'pageId' => $page->id,
            'language' => $request->language,
            'section' => $request->section
        ]);

    }

    public function editorAssurancesUpdate(UpdatePageRequest $request)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $page = Page::find(9);

        if ($request->section == 'header') {
            $this->updateHeader($request);
        }
        if ($request->section == 'footer') {
            $this->updateFooter($request);
        }
        if ($request->section == 'assurances') {
            $section = Section::where('page_id', $page->id)->where('section_type', 'assurances')->first();

            if ($section) {

                // 1. title_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_1,
                    'sort_order' => 0,
                    'groups' => 'title_1',
                    'language' => $request->language,
                ]);

                // 2. description_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_1,
                    'sort_order' => 0,
                    'groups' => 'description_1',
                    'language' => $request->language,
                ]);

                // 3. description_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_2,
                    'sort_order' => 0,
                    'groups' => 'description_2',
                    'language' => $request->language,
                ]);

                // 4. description_3
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_3')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_3,
                    'sort_order' => 0,
                    'groups' => 'description_3',
                    'language' => $request->language,
                ]);

                // 5. description_4
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_4')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_4,
                    'sort_order' => 0,
                    'groups' => 'description_4',
                    'language' => $request->language,
                ]);

                // 6. description_5
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_5')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_5,
                    'sort_order' => 0,
                    'groups' => 'description_5',
                    'language' => $request->language,
                ]);

                // 7. description_6
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_6')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_6,
                    'sort_order' => 0,
                    'groups' => 'description_6',
                    'language' => $request->language,
                ]);

                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_2,
                    'sort_order' => 0,
                    'groups' => 'title_2',
                    'language' => $request->language,
                ]);

                // 8. description_7
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_7')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_7,
                    'sort_order' => 0,
                    'groups' => 'description_7',
                    'language' => $request->language,
                ]);

                // list_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_1')->delete();
                $list_1 = json_decode($request->list_1, true);

                if ($list_1 != null) {
                    foreach ($list_1 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_1',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

            }
        }

        return redirect()->route('admin.editors.show', [
            'pageId' => $page->id,
            'language' => $request->language,
            'section' => $request->section
        ]);

    }

    public function editorLocalisationsUpdate(UpdatePageRequest $request)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN);
        $page = Page::find(6);

        if ($request->section == 'header') {
            $this->updateHeader($request);
        }
        if ($request->section == 'footer') {
            $this->updateFooter($request);
        }
        if ($request->section == 'localisations') {

            $section = Section::where('page_id', $page->id)->where('section_type', 'localisations')->first();

            if ($section) {

                // 1. title_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_1,
                    'sort_order' => 0,
                    'groups' => 'title_1',
                    'language' => $request->language,
                ]);

                // 2. description_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_1,
                    'sort_order' => 0,
                    'groups' => 'description_1',
                    'language' => $request->language,
                ]);

                // 3. list_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_1')->delete();
                $list_1 = json_decode($request->list_1, true);

                if ($list_1 != null) {
                    foreach ($list_1 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'content' => $value['style'],
                                'sort_order' => $key,
                                'groups' => 'list_1',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 4. list_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_2')->delete();
                $list_2 = json_decode($request->list_2, true);

                if ($list_2 != null) {
                    foreach ($list_2 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_2',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 5. list_3
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_3')->delete();
                $list_3 = json_decode($request->list_3, true);

                if ($list_3 != null) {
                    foreach ($list_3 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_3',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }
            }
        }

        return redirect()->route('admin.editors.show', [
            'pageId' => $page->id,
            'language' => $request->language,
            'section' => $request->section
        ]);

    }

    public function editorComoresUpdate(UpdatePageRequest $request)
    {
        abort_if(Gate::denies('page_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');
        $page = Page::find(10);

        if ($request->section == 'header') {
            $this->updateHeader($request);
        }
        if ($request->section == 'footer') {
            $this->updateFooter($request);
        }
        if ($request->section == 'comores') {

            $section = Section::where('page_id', $page->id)->where('section_type', 'comores')->first();

            if ($section) {

                // 1. title_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_1,
                    'sort_order' => 0,
                    'groups' => 'title_1',
                    'language' => $request->language,
                ]);

                // 2. title_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_2,
                    'sort_order' => 0,
                    'groups' => 'title_2',
                    'language' => $request->language,
                ]);

                // 3. title_3
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'title_3')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->title_3,
                    'sort_order' => 0,
                    'groups' => 'title_3',
                    'language' => $request->language,
                ]);

                // 4. list_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_1')->delete();
                $list_1 = json_decode($request->list_1, true);

                if ($list_1 != null) {
                    foreach ($list_1 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_1',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 5. description_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'description_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'title' => $request->description_1,
                    'sort_order' => 0,
                    'groups' => 'description_1',
                    'language' => $request->language,
                ]);

                // 6. list_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'list_2')->delete();
                $list_2 = json_decode($request->list_2, true);

                if ($list_2 != null) {
                    foreach ($list_2 as $key => $value) {

                        if ($value['title'] && $value['url']) {
                            SectionItem::create([
                                'section_id' => $section->id,
                                'title' => $value['title'],
                                'url' => $value['url'],
                                'sort_order' => $key,
                                'groups' => 'list_2',
                                'language' => $request->language,
                            ]);
                        }
                    }
                }

                // 7. image_1
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'image_1')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'image' => $request->image_1,
                    'sort_order' => 0,
                    'groups' => 'image_1',
                    'language' => $request->language,
                ]);

                // 8. image_2
                SectionItem::where('section_id', $section->id)->where('language', $request->language)->where('groups', 'image_2')->delete();
                SectionItem::create([
                    'section_id' => $section->id,
                    'image' => $request->image_2,
                    'sort_order' => 0,
                    'groups' => 'image_2',
                    'language' => $request->language,
                ]);

            }
        }

        return redirect()->route('admin.editors.show', [
            'pageId' => $page->id,
            'language' => $request->language,
            'section' => $request->section
        ]);

    }

}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Controllers\Traits\MediaUploadingTrait;
use App\Http\Requests\MassDestroySectionItemRequest;
use App\Http\Requests\StoreSectionItemRequest;
use App\Http\Requests\UpdateSectionItemRequest;
use App\Models\Section;
use App\Models\SectionItem;
use Gate;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class SectionItemController extends Controller
{
    use MediaUploadingTrait, CsvImportTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('section_item_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = SectionItem::with(['section'])->select(sprintf('%s.*', (new SectionItem)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'section_item_show';
                $editGate      = 'section_item_edit';
                $deleteGate    = 'section_item_delete';
                $crudRoutePart = 'section-items';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                return $row->id ? $row->id : '';
            });
            $table->addColumn('section_section_type', function ($row) {
                return $row->section ? $row->section->section_type : '';
            });

            $table->editColumn('section.title', function ($row) {
                return $row->section ? (is_string($row->section) ? $row->section : $row->section->title) : '';
            });
            $table->editColumn('title', function ($row) {
                return $row->title ? $row->title : '';
            });
            $table->editColumn('url', function ($row) {
                return $row->url ? $row->url : '';
            });
            $table->editColumn('image', function ($row) {
                if ($photo = $row->image) {
                    return sprintf(
                        '<a href="%s" target="_blank"><img src="%s" width="50px" height="50px"></a>',
                        $photo->url,
                        $photo->thumbnail
                    );
                }

                return '';
            });
            $table->editColumn('content', function ($row) {
                return $row->content ? $row->content : '';
            });
            $table->editColumn('sort_order', function ($row) {
                return $row->sort_order ? $row->sort_order : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'section', 'image']);

            return $table->make(true);
        }

        return view('admin.sectionItems.index');
    }

    public function create()
    {
        abort_if(Gate::denies('section_item_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $sections = Section::pluck('section_type', 'id')->prepend(trans('global.pleaseSelect'), '');

        return view('admin.sectionItems.create', compact('sections'));
    }

    public function store(StoreSectionItemRequest $request)
    {
        $sectionItem = SectionItem::create($request->all());

        if ($request->input('image', false)) {
            $sectionItem->addMedia(storage_path('tmp/uploads/' . basename($request->input('image'))))->toMediaCollection('image');
        }

        if ($media = $request->input('ck-media', false)) {
            Media::whereIn('id', $media)->update(['model_id' => $sectionItem->id]);
        }

        return redirect()->route('admin.section-items.index');
    }

    public function edit(SectionItem $sectionItem)
    {
        abort_if(Gate::denies('section_item_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $sections = Section::pluck('section_type', 'id')->prepend(trans('global.pleaseSelect'), '');

        $sectionItem->load('section');

        return view('admin.sectionItems.edit', compact('sectionItem', 'sections'));
    }

    public function update(UpdateSectionItemRequest $request, SectionItem $sectionItem)
    {
        $sectionItem->update($request->all());

        if ($request->input('image', false)) {
            if (! $sectionItem->image || $request->input('image') !== $sectionItem->image->file_name) {
                if ($sectionItem->image) {
                    $sectionItem->image->delete();
                }
                $sectionItem->addMedia(storage_path('tmp/uploads/' . basename($request->input('image'))))->toMediaCollection('image');
            }
        } elseif ($sectionItem->image) {
            $sectionItem->image->delete();
        }

        return redirect()->route('admin.section-items.index');
    }

    public function show(SectionItem $sectionItem)
    {
        abort_if(Gate::denies('section_item_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $sectionItem->load('section');

        return view('admin.sectionItems.show', compact('sectionItem'));
    }

    public function destroy(SectionItem $sectionItem)
    {
        abort_if(Gate::denies('section_item_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $sectionItem->delete();

        return back();
    }

    public function massDestroy(MassDestroySectionItemRequest $request)
    {
        $sectionItems = SectionItem::find(request('ids'));

        foreach ($sectionItems as $sectionItem) {
            $sectionItem->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function storeCKEditorImages(Request $request)
    {
        abort_if(Gate::denies('section_item_create') && Gate::denies('section_item_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $model         = new SectionItem();
        $model->id     = $request->input('crud_id', 0);
        $model->exists = true;
        $media         = $model->addMediaFromRequest('upload')->toMediaCollection('ck-media');

        return response()->json(['id' => $media->id, 'url' => $media->getUrl()], Response::HTTP_CREATED);
    }
}

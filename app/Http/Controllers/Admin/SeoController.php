<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Controllers\Traits\MediaUploadingTrait;
use App\Http\Requests\MassDestroySeoRequest;
use App\Http\Requests\StoreSeoRequest;
use App\Http\Requests\UpdateSeoRequest;
use App\Models\Page;
use App\Models\Seo;
use Gate;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class SeoController extends Controller
{
    use MediaUploadingTrait, CsvImportTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('seo_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = Seo::with(['page'])->select(sprintf('%s.*', (new Seo)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'seo_show';
                $editGate      = 'seo_edit';
                $deleteGate    = 'seo_delete';
                $crudRoutePart = 'seos';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                return $row->id ? $row->id : '';
            });
            $table->editColumn('meta_title', function ($row) {
                return $row->meta_title ? $row->meta_title : '';
            });
            $table->editColumn('meta_description', function ($row) {
                return $row->meta_description ? $row->meta_description : '';
            });
            $table->editColumn('meta_keywords', function ($row) {
                return $row->meta_keywords ? $row->meta_keywords : '';
            });
            $table->editColumn('canonical_url', function ($row) {
                return $row->canonical_url ? $row->canonical_url : '';
            });
            $table->editColumn('og_image', function ($row) {
                if ($photo = $row->og_image) {
                    return sprintf(
                        '<a href="%s" target="_blank"><img src="%s" width="50px" height="50px"></a>',
                        $photo->url,
                        $photo->thumbnail
                    );
                }

                return '';
            });
            $table->editColumn('og_type', function ($row) {
                return $row->og_type ? $row->og_type : '';
            });
            $table->editColumn('schema_type', function ($row) {
                return $row->schema_type ? $row->schema_type : '';
            });
            $table->editColumn('schema_json', function ($row) {
                return $row->schema_json ? $row->schema_json : '';
            });
            $table->editColumn('sitemap_priority', function ($row) {
                return $row->sitemap_priority ? $row->sitemap_priority : '';
            });
            $table->editColumn('sitemap_frequency', function ($row) {
                return $row->sitemap_frequency ? $row->sitemap_frequency : '';
            });
            $table->editColumn('index', function ($row) {
                return '<input type="checkbox" disabled ' . ($row->index ? 'checked' : null) . '>';
            });
            $table->addColumn('page_title', function ($row) {
                return $row->page ? $row->page->title : '';
            });

            $table->editColumn('follow', function ($row) {
                return '<input type="checkbox" disabled ' . ($row->follow ? 'checked' : null) . '>';
            });

            $table->rawColumns(['actions', 'placeholder', 'og_image', 'index', 'page', 'follow']);

            return $table->make(true);
        }

        return view('admin.seos.index');
    }

    public function create()
    {
        abort_if(Gate::denies('seo_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $pages = Page::pluck('title', 'id')->prepend(trans('global.pleaseSelect'), '');

        return view('admin.seos.create', compact('pages'));
    }

    public function store(StoreSeoRequest $request)
    {
        $seo = Seo::create($request->all());

        if ($request->input('og_image', false)) {
            $seo->addMedia(storage_path('tmp/uploads/' . basename($request->input('og_image'))))->toMediaCollection('og_image');
        }

        if ($media = $request->input('ck-media', false)) {
            Media::whereIn('id', $media)->update(['model_id' => $seo->id]);
        }

        return redirect()->route('admin.seos.index');
    }

    public function edit(Seo $seo)
    {
        abort_if(Gate::denies('seo_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $pages = Page::pluck('title', 'id')->prepend(trans('global.pleaseSelect'), '');

        $seo->load('page');

        return view('admin.seos.edit', compact('pages', 'seo'));
    }

    public function update(UpdateSeoRequest $request, Seo $seo)
    {
        $seo->update($request->all());

        if ($request->input('og_image', false)) {
            if (! $seo->og_image || $request->input('og_image') !== $seo->og_image->file_name) {
                if ($seo->og_image) {
                    $seo->og_image->delete();
                }
                $seo->addMedia(storage_path('tmp/uploads/' . basename($request->input('og_image'))))->toMediaCollection('og_image');
            }
        } elseif ($seo->og_image) {
            $seo->og_image->delete();
        }

        return redirect()->route('admin.seos.index');
    }

    public function show(Seo $seo)
    {
        abort_if(Gate::denies('seo_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $seo->load('page');

        return view('admin.seos.show', compact('seo'));
    }

    public function destroy(Seo $seo)
    {
        abort_if(Gate::denies('seo_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $seo->delete();

        return back();
    }

    public function massDestroy(MassDestroySeoRequest $request)
    {
        $seos = Seo::find(request('ids'));

        foreach ($seos as $seo) {
            $seo->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function storeCKEditorImages(Request $request)
    {
        abort_if(Gate::denies('seo_create') && Gate::denies('seo_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $model         = new Seo();
        $model->id     = $request->input('crud_id', 0);
        $model->exists = true;
        $media         = $model->addMediaFromRequest('upload')->toMediaCollection('ck-media');

        return response()->json(['id' => $media->id, 'url' => $media->getUrl()], Response::HTTP_CREATED);
    }
}

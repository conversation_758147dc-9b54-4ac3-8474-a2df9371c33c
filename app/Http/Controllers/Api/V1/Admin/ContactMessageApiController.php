<?php

namespace App\Http\Controllers\Api\V1\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreContactMessageRequest;
use App\Http\Requests\UpdateContactMessageRequest;
use App\Http\Resources\Admin\ContactMessageResource;
use App\Models\ContactMessage;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ContactMessageApiController extends Controller
{
    public function index()
    {
        abort_if(Gate::denies('contact_message_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return new ContactMessageResource(ContactMessage::all());
    }

    public function store(StoreContactMessageRequest $request)
    {
        $contactMessage = ContactMessage::create($request->all());

        return (new ContactMessageResource($contactMessage))
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }

    public function show(ContactMessage $contactMessage)
    {
        abort_if(Gate::denies('contact_message_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return new ContactMessageResource($contactMessage);
    }

    public function update(UpdateContactMessageRequest $request, ContactMessage $contactMessage)
    {
        $contactMessage->update($request->all());

        return (new ContactMessageResource($contactMessage))
            ->response()
            ->setStatusCode(Response::HTTP_ACCEPTED);
    }

    public function destroy(ContactMessage $contactMessage)
    {
        abort_if(Gate::denies('contact_message_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $contactMessage->delete();

        return response(null, Response::HTTP_NO_CONTENT);
    }
}

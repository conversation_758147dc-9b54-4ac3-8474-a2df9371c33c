<?php

namespace App\Http\Controllers\Api\V1\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\MediaUploadingTrait;
use App\Http\Requests\StoreSectionItemRequest;
use App\Http\Requests\UpdateSectionItemRequest;
use App\Http\Resources\Admin\SectionItemResource;
use App\Models\SectionItem;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SectionItemApiController extends Controller
{
    use MediaUploadingTrait;

    public function index()
    {
        abort_if(Gate::denies('section_item_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return new SectionItemResource(SectionItem::with(['section'])->get());
    }

    public function store(StoreSectionItemRequest $request)
    {
        $sectionItem = SectionItem::create($request->all());

        if ($request->input('image', false)) {
            $sectionItem->addMedia(storage_path('tmp/uploads/' . basename($request->input('image'))))->toMediaCollection('image');
        }

        return (new SectionItemResource($sectionItem))
            ->response()
            ->setStatusCode(Response::HTTP_CREATED);
    }

    public function show(SectionItem $sectionItem)
    {
        abort_if(Gate::denies('section_item_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return new SectionItemResource($sectionItem->load(['section']));
    }

    public function update(UpdateSectionItemRequest $request, SectionItem $sectionItem)
    {
        $sectionItem->update($request->all());

        if ($request->input('image', false)) {
            if (! $sectionItem->image || $request->input('image') !== $sectionItem->image->file_name) {
                if ($sectionItem->image) {
                    $sectionItem->image->delete();
                }
                $sectionItem->addMedia(storage_path('tmp/uploads/' . basename($request->input('image'))))->toMediaCollection('image');
            }
        } elseif ($sectionItem->image) {
            $sectionItem->image->delete();
        }

        return (new SectionItemResource($sectionItem))
            ->response()
            ->setStatusCode(Response::HTTP_ACCEPTED);
    }

    public function destroy(SectionItem $sectionItem)
    {
        abort_if(Gate::denies('section_item_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $sectionItem->delete();

        return response(null, Response::HTTP_NO_CONTENT);
    }
}

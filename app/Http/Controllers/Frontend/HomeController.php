<?php

namespace App\Http\Controllers\Frontend;

use App\Models\Section;
use App\Utils\SectionUtils;
use App\Models\Page;
use Illuminate\Http\Request;
use App\Models\ContactMessage;
use App\Models\Setting;

class HomeController
{
    public function index()
    {
        $page = Page::find(1);
        $meta_description = $page->meta_description ?? '';
        $meta_keywords = $page->meta_keywords ?? '';
        $language = app()->getLocale();
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, 'index', $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $settings = Setting::pluck('meta_value', 'meta_key')->toArray();
        $bg = '';

        return view('frontend.index', compact(
            'section_header',
            'section_data',
            'section_footer',
            'meta_description',
            'meta_keywords',
            'settings',
            'page'
        ));
    }

    public function missions()
    {
        $page = Page::find(2);
        $meta_description = $page->meta_description ?? '';
        $meta_keywords = $page->meta_keywords ?? '';
        $language = app()->getLocale();
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, 'missions', $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $settings = Setting::pluck('meta_value', 'meta_key')->toArray();

        return view('frontend.index', compact(
            'section_header',
            'section_data',
            'section_footer',
            'meta_description',
            'meta_keywords',
            'settings',
            'page'
        ));
    }

    public function bureaux()
    {
        $page = Page::find(3);
        $meta_description = $page->meta_description ?? '';
        $meta_keywords = $page->meta_keywords ?? '';
        $language = app()->getLocale();
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, 'bureaux', $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $settings = Setting::pluck('meta_value', 'meta_key')->toArray();

        return view('frontend.index', compact(
            'section_header',
            'section_data',
            'section_footer',
            'meta_description',
            'meta_keywords',
            'settings',
            'page'
        ));
    }

    public function introduction()
    {
        $page = Page::find(4);
        $meta_description = $page->meta_description ?? '';
        $meta_keywords = $page->meta_keywords ?? '';
        $language = app()->getLocale();
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, 'introduction', $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $settings = Setting::pluck('meta_value', 'meta_key')->toArray();

        return view('frontend.index', compact(
            'section_header',
            'section_data',
            'section_footer',
            'meta_description',
            'meta_keywords',
            'settings',
            'page'
        ));
    }

    public function professionnels()
    {
        $page = Page::find(5);
        $meta_description = $page->meta_description ?? '';
        $meta_keywords = $page->meta_keywords ?? '';
        $language = app()->getLocale();
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, 'professionnels', $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $settings = Setting::pluck('meta_value', 'meta_key')->toArray();

        return view('frontend.index', compact(
            'section_header',
            'section_data',
            'section_footer',
            'meta_description',
            'meta_keywords',
            'settings',
            'page'
        ));
    }

    public function assurances()
    {
        $page = Page::find(9);
        $meta_description = $page->meta_description ?? '';
        $meta_keywords = $page->meta_keywords ?? '';
        $language = app()->getLocale();
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, 'assurances', $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $settings = Setting::pluck('meta_value', 'meta_key')->toArray();

        return view('frontend.index', compact(
            'section_header',
            'section_data',
            'section_footer',
            'meta_description',
            'meta_keywords',
            'settings',
            'page'
        ));
    }

    // localisations

    public function localisations()
    {
        $page = Page::find(6);
        $meta_description = $page->meta_description ?? '';
        $meta_keywords = $page->meta_keywords ?? '';
        $language = app()->getLocale();
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, 'localisations', $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $settings = Setting::pluck('meta_value', 'meta_key')->toArray();

        return view('frontend.index', compact(
            'section_header',
            'section_data',
            'section_footer',
            'meta_description',
            'meta_keywords',
            'settings',
            'page'
        ));
    }

    public function comores()
    {
        $page = Page::find(10);
        $meta_description = $page->meta_description ?? '';
        $meta_keywords = $page->meta_keywords ?? '';
        $language = app()->getLocale();
        $section_header = SectionUtils::getSectionHeaderData($language);
        $section_data =  SectionUtils::getSectionData($language, 'comores', $page->id);
        $section_footer = SectionUtils::getSectionFooterData($language);
        $settings = Setting::pluck('meta_value', 'meta_key')->toArray();

        return view('frontend.index', compact(
            'section_header',
            'section_data',
            'section_footer',
            'meta_description',
            'meta_keywords',
            'settings',
            'page'
        ));
    }





    

    public function contactStore(Request $request)
    {

        $contact = new ContactMessage();
        $contact->name = $request->name;
        $contact->email = $request->email;
        $contact->message = $request->message;
        $contact->status = 'unread';
        $contact->save();

        return response()->json(['success' => true]);
    }

    public function changeLanguage($lang)
    {
        if (! in_array($lang, ['en', 'fr'])) {
            $lang = 'fr';
        }

        session(['locale' => $lang]);

        return redirect()->back();
    }
}

<?php

namespace App\Http\Controllers\Traits;

use Illuminate\Http\Request;

trait MediaUploadingTrait
{
    public function storeMedia(Request $request)
    {
        // Validates file size
        if (request()->has('size')) {
            $this->validate(request(), [
                'file' => 'max:' . request()->input('size') * 1024,
            ]);
        }
        // If width or height is preset - we are validating it as an image (but skip for SVG)
        if (request()->has('width') || request()->has('height')) {
            $file = request()->file('file');
            $mimeType = $file ? $file->getMimeType() : null;

            // Skip dimension validation for SVG files as they are vector-based
            if ($mimeType !== 'image/svg+xml') {
                $this->validate(request(), [
                    'file' => sprintf(
                        'image|dimensions:max_width=%s,max_height=%s',
                        request()->input('width', 100000),
                        request()->input('height', 100000)
                    ),
                ]);
            } else {
                // For SVG files, just validate that it's a valid file
                $this->validate(request(), [
                    'file' => 'file|mimes:svg'
                ]);
            }
        }

        $path = storage_path('tmp/uploads');

        try {
            if (! file_exists($path)) {
                mkdir($path, 0755, true);
            }
        } catch (\Exception $e) {
        }

        $file = $request->file('file');

        $name = uniqid() . '_' . trim($file->getClientOriginalName());

        $file->move($path, $name);

        return response()->json([
            'name'          => $name,
            'original_name' => $file->getClientOriginalName(),
        ]);
    }
}

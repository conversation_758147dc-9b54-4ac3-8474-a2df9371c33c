<?php

namespace App\Http\Requests;

use App\Models\ContactMessage;
use Gate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;

class StoreContactMessageRequest extends FormRequest
{
    public function authorize()
    {
        return Gate::allows('contact_message_create');
    }

    public function rules()
    {
        return [
            'name' => [
                'string',
                'nullable',
            ],
        ];
    }
}

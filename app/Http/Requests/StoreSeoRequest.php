<?php

namespace App\Http\Requests;

use App\Models\Seo;
use Gate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;

class StoreSeoRequest extends FormRequest
{
    public function authorize()
    {
        return Gate::allows('seo_create');
    }

    public function rules()
    {
        return [
            'meta_title' => [
                'string',
                'nullable',
            ],
            'meta_keywords' => [
                'string',
                'nullable',
            ],
            'canonical_url' => [
                'string',
                'nullable',
            ],
            'og_type' => [
                'string',
                'nullable',
            ],
            'schema_type' => [
                'string',
                'nullable',
            ],
            'schema_json' => [
                'string',
                'nullable',
            ],
            'sitemap_priority' => [
                'string',
                'nullable',
            ],
            'sitemap_frequency' => [
                'string',
                'nullable',
            ],
        ];
    }
}

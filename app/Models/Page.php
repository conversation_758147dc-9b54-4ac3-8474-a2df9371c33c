<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Page extends Model implements HasMedia
{
    use SoftDeletes, HasFactory, InteractsWithMedia;

    public $table = 'pages';

    protected $appends = [
        'image',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $fillable = [
        'title',
        'slug',
        'content',
        'admin_page',
        'meta_description',
        'meta_keywords',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function pageSeos()
    {
        return $this->hasMany(Seo::class, 'page_id', 'id');
    }


    public function getImageAttribute()
    {
        $file = $this->getMedia('image')->last();
        if ($file) {
            $file->url = $file->getUrl();

            // For SVG files, use the original file as thumbnail and preview
            if ($file->mime_type === 'image/svg+xml') {
                $file->thumbnail = $file->getUrl();
                $file->preview = $file->getUrl();
            } else {
                // Try to get conversions, fallback to original if they don't exist
                try {
                    $file->thumbnail = $file->getUrl('thumb');
                    $file->preview = $file->getUrl('preview');
                } catch (\Exception $e) {
                    $file->thumbnail = $file->getUrl();
                    $file->preview = $file->getUrl();
                }
            }
        }

        return $file;
    }

    public function registerMediaConversions(Media $media = null): void
    {
        // Skip conversions for SVG files as they are vector-based
        if ($media && $media->mime_type === 'image/svg+xml') {
            return;
        }

        $this->addMediaConversion('thumb')->fit('crop', 150, 150);
        $this->addMediaConversion('preview')->fit('crop', 120, 120);
    }


}

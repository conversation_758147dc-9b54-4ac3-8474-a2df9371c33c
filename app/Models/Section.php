<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Section extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia, HasFactory;

    public $table = 'sections';


    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const SECTION_TYPE_SELECT = [
        'header'   => 'Header',
        'carousel' => 'Carousel',
        'apropos'  => 'Apropos',
        'casting'  => 'Casting',
        'jigging'  => 'Jigging',
        'traine'   => 'Traine',
        'excursions' => 'Excursions',
        'footer'   => 'Footer',
        'contact'  => 'Contact',
        'social'   => 'Social',
        'about'    => 'About',
        'gallery'  => 'Gallery',
        'map'      => 'Map',
    ];

    protected $fillable = [
        'page_id',
        'section_type',
        'title',
        'text',
        'sort_order',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')->fit('crop', 50, 50);
        $this->addMediaConversion('preview')->fit('crop', 120, 120);
    }

    public function sectionSectionItems()
    {
        return $this->hasMany(SectionItem::class, 'section_id', 'id');
    }

    public function page()
    {
        return $this->belongsTo(Page::class, 'page_id');
    }

}

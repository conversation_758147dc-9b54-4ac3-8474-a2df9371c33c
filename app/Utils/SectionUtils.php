<?php

namespace App\Utils;

use App\Models\Section;
use App\Models\SectionItem;

class SectionUtils
{
    public static function getSectionHeaderData($language = 'fr')
    {
        /*
            Section Header ID : 1
            Section items
            - Logo groups : logo
            - Menu List groups : menu_list
        */

        $section_header = Section::where('section_type', 'header')->first();

        // Logo
        $section_item_logo = SectionItem::where('section_id', $section_header->id)
        ->where('groups', 'logo')
        ->first();
        $section_logo = $section_item_logo->image ?? '';

        // Menu List
        $section_item_menu_list = SectionItem::where('section_id', $section_header->id)
        ->where('language', $language)
        ->where('groups', 'menu_list')
        ->orderBy('sort_order', 'asc')
        ->get();

        $section_menu_list = [];
        foreach ($section_item_menu_list as $item) {

            if($item->url) {
                $section_menu_list[] = [
                    'name' => $item->title,
                    'link' => $item->url,
                ];
            }
        }

        $section_header_data = collect([
            'logo' => $section_logo,
            'menu_list' => $section_menu_list
        ])->all();

        return (object) $section_header_data;
    }

    public static function getSectionFooterData($language = 'fr')
    {
        /*
            Section Footer ID : 12
            Section items
            - Footer Copyright groups : copyright
        */

        $section_footer = Section::where('section_type', 'footer')->first();

        $section_item_footer_copyright = SectionItem::where('section_id', $section_footer->id)
        ->where('groups', 'copyright')
        ->where('language', $language)
        ->first();
        $section_footer_copyright = $section_item_footer_copyright->title ?? '';

        $section_footer_data = collect([
            'copyright' => $section_footer_copyright,
        ])->all();

        return (object) $section_footer_data;

    }

    public static function getSectionData($language = 'fr', $section_type, $page_id)
    {
        $section = Section::where('section_type', $section_type)->where('page_id', $page_id)->first();

        if ($section_type == 'index')
        {

            $section_item_title_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_1')
            ->where('language', $language)
            ->first();
            $section_item_title_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_2')
            ->where('language', $language)
            ->first();
            $section_item_list_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_1')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_title_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_3')
            ->where('language', $language)
            ->first();
            $section_item_list_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_2')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_list_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_3')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();

            $section_item_title_1 = $section_item_title_1->title ?? '';
            $section_item_list_1 = $section_item_list_1->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_title_2 = $section_item_title_2->title ?? '';
            $section_item_list_2 = $section_item_list_2->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_title_3 = $section_item_title_3->title ?? '';

            $section_item_list_3 = $section_item_list_3->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();

            $section_data = collect([
                'title_1' => $section_item_title_1,
                'title_2' => $section_item_title_2,
                'list_1' => $section_item_list_1,
                'title_3' => $section_item_title_3,
                'list_2' => $section_item_list_2,
                'list_3' => $section_item_list_3,
            ])->all();

        }
        elseif ($section_type == 'missions')
        {

            $section_item_title_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_1')
            ->where('language', $language)
            ->first();
            $section_item_title_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_2')
            ->where('language', $language)
            ->first();
            $section_item_list_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_1')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_title_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_3')
            ->where('language', $language)
            ->first();
            $section_item_list_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_2')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_list_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_3')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();

            $section_item_title_1 = $section_item_title_1->title ?? '';
            $section_item_list_1 = $section_item_list_1->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_title_2 = $section_item_title_2->title ?? '';
            $section_item_list_2 = $section_item_list_2->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_title_3 = $section_item_title_3->title ?? '';

            $section_item_list_3 = $section_item_list_3->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();

            $section_data = collect([
                'title_1' => $section_item_title_1,
                'title_2' => $section_item_title_2,
                'list_1' => $section_item_list_1,
                'title_3' => $section_item_title_3,
                'list_2' => $section_item_list_2,
                'list_3' => $section_item_list_3,
            ])->all();
        }
        elseif ($section_type == 'bureaux')
        {

            $section_item_title_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_1')
            ->where('language', $language)
            ->first();
            $section_item_title_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_2')
            ->where('language', $language)
            ->first();
            $section_item_list_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_1')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_list_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_2')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_list_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_3')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();

            $section_item_title_1 = $section_item_title_1->title ?? '';
            $section_item_list_1 = $section_item_list_1->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_title_2 = $section_item_title_2->title ?? '';
            $section_item_list_2 = $section_item_list_2->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_list_3 = $section_item_list_3->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();

            $section_data = collect([
                'title_1' => $section_item_title_1,
                'title_2' => $section_item_title_2,
                'list_1' => $section_item_list_1,
                'list_2' => $section_item_list_2,
                'list_3' => $section_item_list_3,
            ])->all();
        }
        elseif ($section_type == 'introduction')
        {

            $section_item_title_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_1')
            ->where('language', $language)
            ->first();
            $section_item_description_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_1')
            ->where('language', $language)
            ->first();
            $section_item_description_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_2')
            ->where('language', $language)
            ->first();
            $section_item_description_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_3')
            ->where('language', $language)
            ->first();
            $section_item_description_4 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_4')
            ->where('language', $language)
            ->first();
            $section_item_list_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_1')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();


            $section_item_title_1 = $section_item_title_1->title ?? '';
            $section_item_description_1 = $section_item_description_1->title ?? '';
            $section_item_description_2 = $section_item_description_2->title ?? '';
            $section_item_description_3 = $section_item_description_3->title ?? '';
            $section_item_description_4 = $section_item_description_4->title ?? '';

            $section_item_list_1 = $section_item_list_1->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();

            $section_data = collect([
                'title_1' => $section_item_title_1,
                'description_1' => $section_item_description_1,
                'description_2' => $section_item_description_2,
                'description_3' => $section_item_description_3,
                'description_4' => $section_item_description_4,
                'list_1' => $section_item_list_1,
            ])->all();

        }
        elseif ($section_type == 'professionnels')
        {

            $section_item_title_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_1')
            ->where('language', $language)
            ->first();
            $section_item_description_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_1')
            ->where('language', $language)
            ->first();
            $section_item_description_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_2')
            ->where('language', $language)
            ->first();
            $section_item_description_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_3')
            ->where('language', $language)
            ->first();
            $section_item_description_4 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_4')
            ->where('language', $language)
            ->first();
            $section_item_title_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_2')
            ->where('language', $language)
            ->first();
            $section_item_description_5 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_5')
            ->where('language', $language)
            ->first();
            $section_item_list_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_1')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();


            $section_item_title_1 = $section_item_title_1->title ?? '';
            $section_item_description_1 = $section_item_description_1->title ?? '';
            $section_item_description_2 = $section_item_description_2->title ?? '';
            $section_item_description_3 = $section_item_description_3->title ?? '';
            $section_item_description_4 = $section_item_description_4->title ?? '';
            $section_item_title_2 = $section_item_title_2->title ?? '';
            $section_item_description_5 = $section_item_description_5->title ?? '';
            $section_item_list_1 = $section_item_list_1->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();

            $section_data = collect([
                'title_1' => $section_item_title_1,
                'description_1' => $section_item_description_1,
                'description_2' => $section_item_description_2,
                'description_3' => $section_item_description_3,
                'description_4' => $section_item_description_4,
                'title_2' => $section_item_title_2,
                'description_5' => $section_item_description_5,
                'list_1' => $section_item_list_1,
            ])->all();

        }
        elseif ($section_type == 'assurances')
        {

            $section_item_title_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_1')
            ->where('language', $language)
            ->first();
            $section_item_title_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_2')
            ->where('language', $language)
            ->first();
            $section_item_description_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_1')
            ->where('language', $language)
            ->first();
            $section_item_description_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_2')
            ->where('language', $language)
            ->first();
            $section_item_description_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_3')
            ->where('language', $language)
            ->first();
            $section_item_description_4 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_4')
            ->where('language', $language)
            ->first();
            $section_item_description_5 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_5')
            ->where('language', $language)
            ->first();
            $section_item_description_6 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_6')
            ->where('language', $language)
            ->first();
            $section_item_description_7 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_7')
            ->where('language', $language)
            ->first();
            $section_item_list_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_1')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();


            $section_item_title_1 = $section_item_title_1->title ?? '';
            $section_item_title_2 = $section_item_title_2->title ?? '';
            $section_item_description_1 = $section_item_description_1->title ?? '';
            $section_item_description_2 = $section_item_description_2->title ?? '';
            $section_item_description_3 = $section_item_description_3->title ?? '';
            $section_item_description_4 = $section_item_description_4->title ?? '';
            $section_item_description_5 = $section_item_description_5->title ?? '';
            $section_item_description_6 = $section_item_description_6->title ?? '';
            $section_item_description_7 = $section_item_description_7->title ?? '';
            $section_item_list_1 = $section_item_list_1->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();

            $section_data = collect([
                'title_1' => $section_item_title_1,
                'title_2' => $section_item_title_2,
                'description_1' => $section_item_description_1,
                'description_2' => $section_item_description_2,
                'description_3' => $section_item_description_3,
                'description_4' => $section_item_description_4,
                'description_5' => $section_item_description_5,
                'description_6' => $section_item_description_6,
                'description_7' => $section_item_description_7,
                'list_1' => $section_item_list_1,
            ])->all();

        }
        elseif ($section_type == 'localisations')
        {

            $section_item_title_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_1')
            ->where('language', $language)
            ->first();
            $section_item_description_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_1')
            ->where('language', $language)
            ->first();
            $section_item_list_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_1')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_list_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_2')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_list_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_3')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();

            $section_item_title_1 = $section_item_title_1->title ?? '';
            $section_item_description_1 = $section_item_description_1->title ?? '';
            $section_item_list_1 = $section_item_list_1->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                    'style' => $item->content,
                ];
            })->all();
            $section_item_list_2 = $section_item_list_2->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_list_3 = $section_item_list_3->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();

            $section_data = collect([
                'title_1' => $section_item_title_1,
                'description_1' => $section_item_description_1,
                'list_1' => $section_item_list_1,
                'list_2' => $section_item_list_2,
                'list_3' => $section_item_list_3,
            ])->all();

        }
        elseif ($section_type == 'comores')
        {
            $section_item_title_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_1')
            ->where('language', $language)
            ->first();
            $section_item_title_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_2')
            ->where('language', $language)
            ->first();
            $section_item_title_3 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'title_3')
            ->where('language', $language)
            ->first();
            $section_item_list_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_1')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_description_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'description_1')
            ->where('language', $language)
            ->first();
            $section_item_list_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'list_2')
            ->where('language', $language)
            ->orderBy('sort_order', 'asc')
            ->get();
            $section_item_image_1 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'image_1')
            ->where('language', $language)
            ->first();
            $section_item_image_2 = SectionItem::where('section_id', $section->id)
            ->where('groups', 'image_2')
            ->where('language', $language)
            ->first();

            $section_item_title_1 = $section_item_title_1->title ?? '';
            $section_item_title_2 = $section_item_title_2->title ?? '';
            $section_item_title_3 = $section_item_title_3->title ?? '';
            $section_item_list_1 = $section_item_list_1->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_description_1 = $section_item_description_1->title ?? '';
            $section_item_list_2 = $section_item_list_2->map(function ($item) {
                return [
                    'title' => $item->title,
                    'url' => $item->url,
                ];
            })->all();
            $section_item_image_1 = $section_item_image_1->image ?? '';
            $section_item_image_2 = $section_item_image_2->image ?? '';

            $section_data = collect([
                'title_1' => $section_item_title_1,
                'title_2' => $section_item_title_2,
                'title_3' => $section_item_title_3,
                'list_1' => $section_item_list_1,
                'description_1' => $section_item_description_1,
                'list_2' => $section_item_list_2,
                'image_1' => $section_item_image_1,
                'image_2' => $section_item_image_2,
            ])->all();

        }
        else
        {
            // Handle other section types if needed

        }



        return (object) $section_data;

    }

}

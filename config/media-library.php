<?php

return [
    /*
     * The disk on which to store added files and derived images by default. Choose
     * one or more of the disks you've configured in config/filesystems.php.
     */
    'disk_name' => env('MEDIA_DISK', 'public'),

    /*
     * The maximum file size of an item in bytes.
     * Adding a larger file will result in an exception.
     */
    'max_file_size' => 1024 * 1024 * 10, // 10MB

    /*
     * This queue will be used to generate derived and responsive images.
     * Leave empty to use the default queue.
     */
    'queue_name' => env('MEDIA_QUEUE', ''),

    /*
     * By default all conversions will be performed on a queue.
     */
    'queue_conversions_by_default' => env('QUEUE_CONVERSIONS_BY_DEFAULT', true),

    /*
     * The fully qualified class name of the media model.
     */
    'media_model' => Spatie\MediaLibrary\MediaCollections\Models\Media::class,

    /*
     * When enabled, media collections will be serialised using the media library's
     * custom serialiser. This can be useful when using media collections in
     * API responses.
     */
    'use_default_collection_serialization' => true,

    /*
     * The engine that should perform the image conversions.
     * Should be either `gd` or `imagick`.
     */
    'image_driver' => env('IMAGE_DRIVER', 'gd'),

    /*
     * FFMPEG & FFProbe binaries paths, only used if you try to generate video
     * thumbnails and have installed the php-ffmpeg/php-ffmpeg composer package.
     */
    'ffmpeg_path' => env('FFMPEG_PATH', '/usr/bin/ffmpeg'),
    'ffprobe_path' => env('FFPROBE_PATH', '/usr/bin/ffprobe'),

    /*
     * Here you can override the class names of the jobs used by this package. Make sure
     * your custom jobs extend the ones provided by the package.
     */
    'jobs' => [
        'perform_conversions' => \Spatie\MediaLibrary\Conversions\Jobs\PerformConversionsJob::class,
        'generate_responsive_images' => \Spatie\MediaLibrary\ResponsiveImages\Jobs\GenerateResponsiveImagesJob::class,
    ],

    /*
     * When using the addMediaFromUrl method, this class will be called. Leave empty
     * to use the default downloader.
     */
    'media_downloader' => Spatie\MediaLibrary\Downloaders\DefaultDownloader::class,

    /*
     * This is the class that is responsible for naming conversion files. By default,
     * it will use the filename of the original and concatenate the conversion name to it.
     */
    'conversion_file_namer' => Spatie\MediaLibrary\Conversions\DefaultConversionFileNamer::class,

    /*
     * This is the class that is responsible for naming generated paths. By default,
     * it will use the id of the media item and the conversion name to generate a path.
     */
    'path_generator' => Spatie\MediaLibrary\Support\PathGenerator\DefaultPathGenerator::class,

    /*
     * Here you can specify which path generator should be used for the given class.
     */
    'custom_path_generators' => [
        // Model::class => PathGenerator::class
        // or
        // 'model_morph_alias' => PathGenerator::class
    ],

    /*
     * When urls to files get generated, this class will be called. Use the default
     * if your files are stored locally above the site root or on s3.
     */
    'url_generator' => Spatie\MediaLibrary\Support\UrlGenerator\DefaultUrlGenerator::class,

    /*
     * Moves the file to the media library instead of copying it.
     */
    'moves_media_on_update' => false,

    /*
     * Whether to activate versioning when urls to files get generated.
     * When activated, this attaches a ?v=xx query string to the URL.
     */
    'version_urls' => false,

    /*
     * The class that contains the strategy for determining a media file's mime-type.
     */
    'mime_type_detector' => Spatie\MediaLibrary\Support\MediaStream\DefaultMimeTypeDetector::class,

    /*
     * The class that contains the strategy for determining if a media file can have conversions.
     */
    'conversion_generator' => Spatie\MediaLibrary\Conversions\ConversionGenerator::class,

    /*
     * Here you can override the class names of the file adder used by this package.
     */
    'file_adder' => Spatie\MediaLibrary\MediaCollections\FileAdder::class,

    /*
     * When `true`, media collections will throw an exception when trying to add a file
     * that is not accepted by the collection.
     */
    'throw_exception_on_invalid_mime_type' => true,

    /*
     * This is the class responsible for performing the conversion of Media items.
     * In most cases, you won't need to change this.
     */
    'conversion_performer' => Spatie\MediaLibrary\Conversions\ConversionPerformer::class,

    /*
     * When generating converted images, this class will be used to apply effects.
     */
    'image_generators' => [
        Spatie\MediaLibrary\Conversions\ImageGenerators\Image::class,
        Spatie\MediaLibrary\Conversions\ImageGenerators\Webp::class,
        Spatie\MediaLibrary\Conversions\ImageGenerators\Pdf::class,
        Spatie\MediaLibrary\Conversions\ImageGenerators\Svg::class,
        Spatie\MediaLibrary\Conversions\ImageGenerators\Video::class,
    ],

    /*
     * Optimize generated images by removing metadata and applying compression.
     */
    'image_optimizers' => [
        Spatie\ImageOptimizer\Optimizers\Jpegoptim::class => [
            '-m85', // set maximum quality to 85%
            '--force', // ensure that progressive generation is always done also if a little bigger
            '--strip-all', // this strips out all text information such as comments and EXIF data
            '--all-progressive', // this will make sure the resulting image is a progressive one
        ],
        Spatie\ImageOptimizer\Optimizers\Pngquant::class => [
            '--force', // required parameter for this package
        ],
        Spatie\ImageOptimizer\Optimizers\Optipng::class => [
            '-i0', // this will result in a non-interlaced, progressive scanned image
            '-o2', // this set the optimization level to two (multiple IDAT compression trials)
            '-quiet', // required parameter for this package
        ],
        Spatie\ImageOptimizer\Optimizers\Svgo::class => [
            '--disable=cleanupIDs', // disabling because it is known to cause troubles
        ],
        Spatie\ImageOptimizer\Optimizers\Gifsicle::class => [
            '-b', // required parameter for this package
            '-O3', // this produces the slowest but best results
        ],
        Spatie\ImageOptimizer\Optimizers\Cwebp::class => [
            '-m', '6', // for the slowest compression method in order to get the best compression.
            '-pass', '10', // for maximizing the amount of analysis pass.
            '-mt', // multithreading for some speed improvements.
            '-q', '90', //quality factor that brings the least noticeable changes.
        ],
    ],

    /*
     * These generators will be used to create an image of media files.
     */
    'temporary_directory_path' => null,

    /*
     * IPTC, Exif, and XMP metadata will be stripped from the image to reduce file size.
     * Set this value to `false` if you want to preserve this data on the image.
     */
    'strip_exif_data' => true,

    /*
     * This is the maximum time in seconds that can be used for remote downloads.
     * Setting this to a higher value will allow bigger file downloads at the cost of
     * increased execution time.
     */
    'remote_max_execution_time' => 60,

    /*
     * The amount of seconds before a conversion times out.
     */
    'conversion_timeout' => 120,

    /*
     * Path to the temporary directory used for image conversions.
     * If set to null, storage_path('media-library/temp') will be used.
     */
    'temporary_upload_path' => env('MEDIA_TEMP_PATH', null),

    /*
     * Here you can specify which conversions should be performed when a media file gets added.
     * Only the conversions specified in this array will be performed.
     * An empty array means that all conversions will be performed.
     */
    'perform_conversions' => [
        // 'thumb',
        // 'preview',
    ],

    /*
     * When this option is enabled, the package will not delete the original file after
     * performing conversions. This can be useful when you want to keep the original file.
     */
    'delete_original_after_conversions' => false,
];

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSeosTable extends Migration
{
    public function up()
    {
        Schema::create('seos', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('meta_title')->nullable();
            $table->longText('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('canonical_url')->nullable();
            $table->string('og_type')->nullable();
            $table->string('schema_type')->nullable();
            $table->string('schema_json')->nullable();
            $table->string('sitemap_priority')->nullable();
            $table->string('sitemap_frequency')->nullable();
            $table->boolean('index')->default(0)->nullable();
            $table->boolean('follow')->default(0)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }
}

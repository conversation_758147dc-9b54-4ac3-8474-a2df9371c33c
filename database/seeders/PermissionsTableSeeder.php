<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionsTableSeeder extends Seeder
{
    public function run()
    {
        $permissions = [
            [
                'id'    => 1,
                'title' => 'user_management_access',
            ],
            [
                'id'    => 2,
                'title' => 'permission_create',
            ],
            [
                'id'    => 3,
                'title' => 'permission_edit',
            ],
            [
                'id'    => 4,
                'title' => 'permission_show',
            ],
            [
                'id'    => 5,
                'title' => 'permission_delete',
            ],
            [
                'id'    => 6,
                'title' => 'permission_access',
            ],
            [
                'id'    => 7,
                'title' => 'role_create',
            ],
            [
                'id'    => 8,
                'title' => 'role_edit',
            ],
            [
                'id'    => 9,
                'title' => 'role_show',
            ],
            [
                'id'    => 10,
                'title' => 'role_delete',
            ],
            [
                'id'    => 11,
                'title' => 'role_access',
            ],
            [
                'id'    => 12,
                'title' => 'user_create',
            ],
            [
                'id'    => 13,
                'title' => 'user_edit',
            ],
            [
                'id'    => 14,
                'title' => 'user_show',
            ],
            [
                'id'    => 15,
                'title' => 'user_delete',
            ],
            [
                'id'    => 16,
                'title' => 'user_access',
            ],
            [
                'id'    => 17,
                'title' => 'page_create',
            ],
            [
                'id'    => 18,
                'title' => 'page_edit',
            ],
            [
                'id'    => 19,
                'title' => 'page_show',
            ],
            [
                'id'    => 20,
                'title' => 'page_delete',
            ],
            [
                'id'    => 21,
                'title' => 'page_access',
            ],
            [
                'id'    => 22,
                'title' => 'setting_create',
            ],
            [
                'id'    => 23,
                'title' => 'setting_edit',
            ],
            [
                'id'    => 24,
                'title' => 'setting_show',
            ],
            [
                'id'    => 25,
                'title' => 'setting_delete',
            ],
            [
                'id'    => 26,
                'title' => 'setting_access',
            ],
            [
                'id'    => 27,
                'title' => 'seo_create',
            ],
            [
                'id'    => 28,
                'title' => 'seo_edit',
            ],
            [
                'id'    => 29,
                'title' => 'seo_show',
            ],
            [
                'id'    => 30,
                'title' => 'seo_delete',
            ],
            [
                'id'    => 31,
                'title' => 'seo_access',
            ],
            [
                'id'    => 32,
                'title' => 'section_create',
            ],
            [
                'id'    => 33,
                'title' => 'section_edit',
            ],
            [
                'id'    => 34,
                'title' => 'section_show',
            ],
            [
                'id'    => 35,
                'title' => 'section_delete',
            ],
            [
                'id'    => 36,
                'title' => 'section_access',
            ],
            [
                'id'    => 37,
                'title' => 'section_item_create',
            ],
            [
                'id'    => 38,
                'title' => 'section_item_edit',
            ],
            [
                'id'    => 39,
                'title' => 'section_item_show',
            ],
            [
                'id'    => 40,
                'title' => 'section_item_delete',
            ],
            [
                'id'    => 41,
                'title' => 'section_item_access',
            ],
            [
                'id'    => 42,
                'title' => 'contact_message_create',
            ],
            [
                'id'    => 43,
                'title' => 'contact_message_edit',
            ],
            [
                'id'    => 44,
                'title' => 'contact_message_show',
            ],
            [
                'id'    => 45,
                'title' => 'contact_message_delete',
            ],
            [
                'id'    => 46,
                'title' => 'contact_message_access',
            ],
            [
                'id'    => 47,
                'title' => 'profile_password_edit',
            ],
        ];

        Permission::insert($permissions);
    }
}

body {
    font-family: "Poppins", sans-serif !important;
}

.dataTables_filter label {
    font-size: 0;
}

.ck-powered-by-balloon {
    display: none !important;
}

.dataTables_filter label input {
    font-size: 14px;
}

div.dataTables_scrollBody {
    border-left: none !important;
    min-height: 200px!important;
}

.table_drop .btn.dropdown-toggle::after {
    content: none !important;
}
label.error{
    color: #f1416c;
}

li {
    list-style-type: none;
}

.textarea-h-10{
    height: 10px !important;
}

#container1 {
    height: 300px;
}

.highcharts-data-table caption {
    padding: 1em 0;
    font-size: 1.2em;
    color: #555;
}

.highcharts-data-table th {
    font-weight: 600;
    padding: 0.5em;
}

.highcharts-data-table td,
.highcharts-data-table th,
.highcharts-data-table caption {
    padding: 0.5em;
}

.highcharts-data-table thead tr,
.highcharts-data-table tr:nth-child(even) {
    background: #f8f8f8;
}

.highcharts-data-table tr:hover {
    background: #f1f7ff;
}

.highcharts-figure {
    margin: 0px;
}

.bg-blue-gradient {
    background: rgb(0,158,247);
    background: linear-gradient(90deg, rgba(0,158,247,1) 0%, rgba(0,212,247,1) 100%);
}

.bg-green-gradient {
    background: rgb(80,205,137);
    background: linear-gradient(90deg, rgba(80,205,137,1) 0%, rgba(80,205,195,1) 100%);
}

.bg-red-gradient {
    background: rgb(241,65,65);
    background: linear-gradient(90deg, rgba(241,65,65,1) 0%, rgba(241,65,108,1) 100%);
}

.bg-purple-gradient {
    background: rgb(197,98,227);
    background: linear-gradient(90deg, rgba(197,98,227,1) 0%, rgba(98,107,227,1) 100%);
}

.bg-custom-gradient1 {
    background: rgb(181,2,235);
    background: linear-gradient(90deg, rgba(181,2,235,1) 0%, rgba(30,136,229,1) 100%);
}

.bg-custom-gradient2 {
    background: rgb(255,179,0);
    background: linear-gradient(90deg, rgba(255,179,0,1) 0%, rgba(184,229,30,1) 100%);
}

.bg-custom-gradient3 {
    background: rgb(117,117,117);
    background: linear-gradient(90deg, rgba(117,117,117,1) 0%, rgba(0,255,226,1) 100%);
}

.bg-custom-gradient4 {
    background: rgb(201,27,216);
    background: linear-gradient(90deg, rgba(201,27,216,1) 0%, rgba(0,255,226,1) 100%);
}

.bg-custom-gradient5 {
    background: rgb(255,100,73);
    background: linear-gradient(90deg, rgba(255,100,73,1) 0%, rgba(255,209,0,1) 100%);
}

.cardCustom .card-body {
    padding: 0px;
}

.cardCustom .cnTop {
    padding-top: 20px !important;
    align-items: center;
    padding: 0 2.25rem;
    position: relative;
}

.card-toolbar-custom {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 2;
}

.card-toolbar-custom i::before {
    color: #fff;
}

.card-toolbar-custom i span,
.card-toolbar-custom i span::before {
    color: #fff;
    display: block;
    width: 4px;
    height: 4px;
    background: #fff;
    margin-left: 2px;
    border-radius: 100%;
}

.cardCustom .amount {
    display: flex;
    flex-direction: column;
}

.cardCustom .amount h2,
.cardCustom .amount h3 {
    margin: 0;
    color: #fff;
}

.cardCustom .amount h2 {
    font-size: 26px;
    margin-bottom: 10px;
    font-weight: 500;
}

.cardCustom .card-body .cnInner {
    height: 180px;
}

.cardCustom .amount h3 {
    font-size: 17px;
    font-weight: 400;
}

.cardCustom .amount .cnLevel {
    font-weight: 500;
    color: #fff;
    padding: 0 0 0 15px;
    margin-bottom: 0px;
    background-size: 13px !important;
}

.cardCustom .amount .cnLevel.up {
    color: #14FF00;
    background: url(../img/vector-green.svg) 0 3px no-repeat;
}
.cardCustom .amount .cnLevel.down {
    color: #FF0E28;
    background: url(../img/vector-red.svg) 0 3px no-repeat;
}

/* .cardCustom .cnTop .icon {
    width: 50px;
}

.cardCustom .cnTop .icon i {
    font-size: 50px;
}

.cardCustom .cnTop .icon i span::before {
    color: #fff;
} */

.cardCustom .cnTop .icon {
    width: 52px;
    height: 52px;
    display: block;
    background-size: 100% !important;
    margin-bottom: 10px;
}

.cardCustom.bg-1 .cnTop .icon {
    background: url(../img/card-icon1.svg) 0 0 no-repeat;
}

.cardCustom.bg-2 .cnTop .icon {
    background: url(../img/card-icon2.svg) 0 0 no-repeat;
}

.cardCustom.bg-3 .cnTop .icon {
    background: url(../img/card-icon3.svg) 0 0 no-repeat;
}

.cardCustom.bg-4 .cnTop .icon {
    background: url(../img/card-icon3.svg) 0 0 no-repeat;
}

.cardCustom .cnBottom {
    flex-direction: column;
    justify-content: center;
    padding: 0 2.25rem;
}

.cardCustom .cnBottom h4 {
    color: #fff;
    font-weight: 400;
    margin: 0px;
    font-size: 12px;
}

.cardCustom .cnBottom h4 span {
    font-weight: 600;
}

.card-body-heading {
    margin-bottom: 40px;
}

.card-body-heading h2 {
    font-size: 18px;
    color: #212121;
}

.card-body-heading h3 {
    color: #979AAF;
    font-size: 12px;
    font-weight: 400;
}

.card-rhone {
    background: #000 url(../img/rhone.svg) 26px -140px no-repeat;
    background-size: 100% !important;
    border-radius: 0.625rem;
}

.card-rhone .card-body-inner {
    padding: 40px 0;
}

.card-rhone .card-body-inner h2 {
    color: #fff;
    font-size: 20px;
}
.card-rhone .card-body-inner h3 {
    color: #979AAF;
    font-size: 14px;
    font-weight: 500;
}

.card-rhone .card-body-inner .cnButton {
    margin-top: 70px;
}

.card-rhone .card-body-inner .cnButton a {
    color: #EF6327;
    background: url(../img/arrow-right-orange.svg) right 0px no-repeat;
    background-size: 20px !important;
    padding-right: 30px;
}

/* .card-body-inner {
    display: flex;
} */

.card-body-inner .cnLeft {
    width: 50%;
}


.card-body-inner .cnLeft ul {
    display: flex;
    justify-content: space-evenly;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: center;
    padding: 0px;
}


.card-body-inner .cnLeft ul li {
    width: 50%;
    margin-bottom: 30px;
}

.card-body-inner .cnLeft ul li .cnTitle {
    font-size: 12px;
    color: #979AAF;
    font-weight: 500;
}

.card-body-inner .cnLeft ul li .cnCount {
    font-size: 20px;
    font-size: 700;
}

.card-body-inner .cnRight {
    /* flex: 3; */
}

.card-body-inner .cnLeft .cnSales {
   font-size: 16px;
   color: #2f3c4b;
   font-weight: 400;
   /* display: flex; */
}

.card-body-inner .cnLeft .cnSales i {
    font-size: 24px;
    margin-right: 5px;
}

.card-body-inner .cnLeft .cnTotal {
   font-size: 16px;
   color: #2f3c4b;
   font-weight: 300;
}

.card-body-inner .cnLeft .cnTotal span {
    display: block;
    font-weight: 600;
    font-size: 40px;
}

.card-body-inner .cnLeft .cnDate {
    font-size: 14px;
    color: #2f3c4b;
    font-weight: 300;
    display: flex;
    position: relative;
    top: -10px;
}

.card-body-inner .cnLeft .cnDate i {
    font-size: 24px;
    margin-right: 5px;
}

#kt_header_user_menu_toggle img {
    border-radius: 100% !important;
}

#kt_header_user_menu_toggle .cursor-pointer.symbol {
    display: flex;
    align-items: center;
}

#kt_header_user_menu_toggle .cursor-pointer.symbol p {
    margin: 0px 10px 0 0;
    color: #6E7494;
    font-size: 12px;
}

.app-nav-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.cnQuickTools {
    display: flex;
    margin-bottom: 0px;
    align-content: center;
    flex-direction: row;
    align-items: center;
}

.cnQuickTools p {
    margin-bottom: 0px;
    font-size: 12px;
    font-weight: 600;
    color: #5E6278;
    margin-right: 10px;
}

.cnQuickTools ul {
    margin: 0 10px 0 0;
    padding: 0px;
    display: flex;
}

.app-navbar-item .cnQuickTools ul {
    margin: 0px;
}


.app-navbar-item .cnQuickTools ul li {
    margin-right: 10px;
}

.app-navbar-item .cnQuickTools ul li:last-child {
    margin-right: 3px;
}

.cnQuickTools ul li {
    margin-right: 5px;
}

.cnQuickTools ul li button {
    width: 25px !important;
    height: 26px !important;
    background-size: 100% !important;
}

.cnQuickTools .btnQuick1 {
    background: url(../img/duotone1.svg) 0 0 no-repeat;
}

.cnQuickTools .btnQuick2 {
    background: url(../img/duotone2.svg) 0 0 no-repeat;
}

.cnQuickTools .btnQuick3 {
    background: url(../img/duotone3.svg) 0 0 no-repeat;
}

.cnQuickTools .btnQuick4 {
    background: url(../img/notifications.svg) 0 0 no-repeat;
    background-size: 15px !important;
    width: 15px !important;
    height: 16px !important;
}

.cnQuickTools .btnQuick5 {
    background: url(../img/gear.svg) 0 0 no-repeat;
    background-size: 15px !important;
    width: 15px !important;
    height: 16px !important;
}

.cnQuickTools .quickSelect {
    border-left: 1px solid #E5E5E5;
    padding-left: 15px;
}

.cnQuickTools .quickSelect select {
    padding: 2px 15px;
    border-radius: 30px;
    padding-right: 30px;
    font-size: 12px;
    background: url(../img/drop-select.svg) no-repeat;
    background-position: calc(100% - 10px) center;
    background-size: 10px;
}

.menu-sub-accordion .menu-item .menu-link  i {
    font-size: 15px !important;
}

.menu-sub-accordion .menu-item .menu-link {
    padding: .4rem 1rem;
}

.menu .col-inner-top {
    display: flex;
    justify-content: space-around;
    padding: 0 1rem;
} 

.menu .col-inner-top p {
    margin: 0px;
    padding: 0px;
    font-size: 12px;
    color: #6E7494;
}

.menu .col-inner-top p.name {
    font-size: 10px;
    font-weight: 700;
}

.menu .col-inner-top p.date {
    font-weight: 700;
    font-size: 32px;
    line-height: 25px;
}

.menu .col-inner-top .cnCol {
    width: 50%;
}

.menu .col-inner-top .cnCol:first-child {
    border-right: 1px solid #E5E5E5;
}

.menu .col-inner-top .cnCol:last-child {
    text-align: center;
}

.menu .col-inner-top 

p.title {
    color: #B6B9C6;
    font-size: 12px;
}

[data-kt-app-layout=light-sidebar] .app-sidebar .menu .menu-item .menu-dash .menu-heading {
    display: block;
    height: 40px;
    line-height: 40px;
    color: #fff !important;
    border-radius: 10px;
    background: #003B71 url(../img/card-icon2.svg) 5px center  no-repeat;
    background-size: 30px;
    padding-left: 40px;
}

.app-nav-left h1 {
    font-size: 18px;
    font-weight: 600;
    color: #3F4254;
    margin-bottom: 5px;

}

.app-nav-left h2 {
    font-size: 13px;
    font-weight: 500;
    color: #979AAF;

}

#response .cnHeading {
    margin-bottom: 5px;
}

#response .cnHeading li a {
    color: #622E8C;
}

#response .cnActions ul {
    margin: 0px;
    padding: 0px;
}

#response .cnActions ul li {
    flex: 1;
    display: flex;
    justify-content: space-between;
    padding: 0;
    list-style: none;
    margin-bottom: 15px;
}

#response .cnActions .cnLeft {
    width: 25%;
    align-items: center;
    display: flex;
}

#response .cnActions .cnLeft p {
    margin: 0px;
    padding: 0px;
}

#response .nav-tabs {
    margin-bottom: 20px !important;
}

#response .cnActions .cnRight {
    width: 75%;
}

#response .tab-content {
    max-height: 700px;
    overflow: scroll;
}

#response .cnForm .rounded {
    margin-bottom: 40px;
}

#response .cnForm .cnText {
    text-align: left;
}

#response .cnForm .cnText i {
    font-size: 17px;
    position: relative;
    top: 3px;
}

#response .cnForm .cnSubmit {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#response .chat_message {
    border: 1px solid #DBDFE9;
    border-radius: .475rem;
    margin-bottom: 20px;
}

#response .chat_message .cnTop {
    display: flex;
    justify-content: space-between;
    padding: 20px;
}

#response .chat_message .cnProfile {
    display: flex;
    align-items: center;
}

#response .chat_message .cnProfile .profile-userpic {
    width: 70px;
    height: 70px;
    border-radius: 100%;
    margin-right: 10px;
}

#response .chat_message .cnProfile .profile-userpic img {
    display: block;
    width: 100%;
    border-radius: 100%;
}

#response .chat_message .cnProfile .name span {
    font-size: 14px;
    font-weight: 500;
    position: relative;
    color: #2f3c4b;
}

#response .chat_message .cnProfile .name span::before {
    width: 7px;
    height: 7px;
    display: block;
    border-radius: 100%;
    background: #f1416c;
    content: "";
    position: absolute;
    top: 7px;
    right: -10px;
}

#response .chat_message .cnNotification {
    display: flex;
    align-items: top;
}

#response .chat_message .cnNotification ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

#response .chat_message .cnNotification li {
    display: inline-block;
    margin-right: 10px;
}

#response .chat_message .cnNotification li {
    color: #2f3c4b;
}

#response .chat_message .cnNotification li .ki-time {
    position: relative;
    top: 4px;
    font-size: 17px;
}

#response .chat_message .cnNotification li .ki-notification {
    font-size: 19px;
    position: relative;
    top: 5px;
}

#response .chat_message .cnNotification li .ki-notification .path1:before {
    opacity: 0.7;
}

#response .chat_message .cnNotification li:last-child {
    margin-right: 0px;
}

#response .chat_message .cnNotification li .card-toolbar-custom {
    position: relative;
}

#response .chat_message .cnNotification li .card-toolbar-custom button {
    width: 22px;
    height: 22px;
}

#response .chat_message .cnNotification li .card-toolbar-custom i::before {
    color: #99A1B7;
}

#response .chat_message .cnMessage {
    padding: 0 20px;
    border-bottom: 1px solid #DBDFE9;
}

#response .chat_message .cnMessage  p {
    color: #2f3c4b;
}

#response .chat_message .cnBottom {
    display: flex;
    justify-content: space-between;
}

#response .chat_message .cnBottom .cnButton {
    width: 100%;
}

#response .chat_message .cnBottom a {
    display: block;
    text-align: center;
    width: 100%;
    padding: 20px;
    color: #2f3c4b;
}

#response .chat_message .cnBottom a:hover {
    background: #f8f8f8;
}

#response .chat_message .cnBottom .cnReply a {
    border-right: 1px solid #DBDFE9;
}

#dashboardMenu .cnTitle {
    margin-bottom: 15px;
}

#dashboardMenu .cnTitle h4 {
    position: relative;
    display: inline-block;
}

#dashboardMenu .cnTitle h4::before {
    width: 100%;
    height: 2px;
    display: block;
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0px;
    border-radius: 100%;
    background: rgb(255,100,73);
    background: linear-gradient(90deg, rgba(255,100,73,1) 0%, rgba(255,209,0,1) 100%);
}

.menuList ul {
    margin: 0px;
    padding: 0px;
}

.menuList ul li {
    width: 18%;
    margin-right: 2%;
    min-height: 120px;
}

.menuList ul li:last-child {
    margin-right: 0px;
}

.menuList ul li a {
    height: 100%;
    display: flex;
    width: 100%;
    justify-content: flex-start;
    flex-direction: row;
    align-items: center;
}

.menuList ul li a i {
    color: #fff;
    font-size: 40px;
    margin-right: 20px;
}

.menuList ul li a span {
    color: #fff;
    font-size: 20px;
    text-transform: uppercase;
}

#conGrid {
    padding: 0px !important;
}

#conGrid td.dropdown {
    position: relative;
    overflow: inherit;
}

#conGrid .hidden-dropdown {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: #ccc;
    top: 0px;
    left: 0px;
    cursor: pointer;
}

#conGrid .arrow-up {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #fff;
    position: absolute;
    top: -6px;
    left: 0px;
    right: 0px;
    margin: auto;
  }

#conGrid .custom-dropdown-overlay {
    position: absolute;
    top: 41px;
    left: 0px;
    width: 100%;
    background-color: #fff;
    border: 1px solid #d6d3d1;
    padding: 6px;
    z-index: 9999;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    display: none;
    border-radius: 5px;
}

#conGrid .custom-dropdown-overlay ul {
    list-style: none;
    padding: 0 5px 10px 5px;
    margin: 0px;
}

#conGrid .custom-dropdown-overlay ul li {
    padding: 2px;
    border: 1px solid #d6d3d1;
    width: 100%;
    text-align: left;
    border-radius: 3px;
    margin-bottom: 7px;
    height: 38px;
    -webkit-transition: all 1s ease;
    transition: all 1s ease;
}

#conGrid .custom-dropdown-overlay ul li:hover {
    border: 1px solid #0369a1;
}

#conGrid .custom-dropdown-overlay ul li a {
    display: flex;
    width: 100%;
    height: 100%;
    line-height: 25px;
    color: #404040;
}

#conGrid .custom-dropdown-overlay ul li a:hover {
    color: #0369a1
}

#conGrid .custom-dropdown-overlay ul li i {
    width: 25px;
    height: 25px;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    line-height: 25px;
    text-align: center;
    margin-right: 5px;
}

#conGrid .custom-dropdown-overlay ul li.first i {
    background: #eab308;
}

#conGrid .custom-dropdown-overlay ul li.second i {
    background: #ef4444;
}

#conGrid .custom-dropdown-overlay ul li.third i {
    background: #22c55e;
}

#conGrid .custom-dropdown-overlay ul li.fourth i {
    background: #71717a;
}

#conGrid .custom-dropdown-overlay ul li.fifth  {
    text-align: center;
    margin-bottom: 0px;
    padding: 0px;
}

#conGrid .custom-dropdown-overlay ul li.fifth i {
    color: #404040;
    font-size: 12px;
    margin-right: 3px;
    width: auto;
}

#conGrid .custom-dropdown-overlay ul li.fifth:hover i {
    color: #0369a1;
}

#conGrid .custom-dropdown-overlay ul li.fifth a {
    display: block;
    background: #e7e5e4;
}

.text-gray-800.cnCustom {
    padding-left: 15px;
}


.custom-dropdown-overlay .label_list .custom_bg {
    height: 38px !important;
    border-radius: 3px;
}

.default_custom_label {
    background: #e7e5e4;
}

/* #conGrid .custom-dropdown-overlay button {
    background: none;
    border: none;
    color: #404040;
    margin: 7px 0 0 0;
    padding: 0px;
}

#conGrid .custom-dropdown-overlay button:hover {
    color: #0369a1;
} */

/* #conGrid .custom-dropdown-overlay1 {
    bottom: -182px !important;
} */

#conGrid .custom-dropdown-overlay1 ul li {
    padding: 0px;
    border: none !important;
}

#conGrid .custom-dropdown-overlay1 ul li a {
    display: block;
    text-align: center;
    color: #fff;
    line-height: 38px;
    border-radius: 3px;
}

#conGrid .custom-dropdown-overlay1 ul li a:hover {
    color: #ffffff !important;
}

.customBoardH .all-board {
    height: calc(100vh - 147px);
}

.edit_label_custom {
    height: 40px;
}

.conGrid {
    height: 100vh; 
    overflow-y: auto; 
    position: relative; 
}

.cn-grid {
    margin-bottom: 20px; 
}

.table-grid thead, .cnGridThead {
    position: sticky;
    top: 0;
    z-index: 10; 
    background-color: #fff;
}

.cn-grid table {
    border-collapse: collapse;
    width: 100%;
    border-top: none !important;
}

.cn-grid table thead tr th {
    border-top: none !important;
}

.cn-grid table thead tr {
    border-top: none !important;
}

.cn-grid tr:first-child th:first-child {
    border-radius: 5px 0 0 0;
    border-left: 1px solid #fff !important;
}

.cn-grid tr:first-child th:last-child {
    border-radius: 0 5px 0 0;
    border-right: 1px solid #fff !important;
}

.grid-color-primary th {
    background-color: #bae6fd !important;
}

.grid-color-primary table {
    border-left: 3px solid #bae6fd !important;
}

.grid-color-secondary th {
    background-color: #fde68a !important;
}

.grid-color-secondary table {
    border-left: 3px solid #fde68a !important;
}

.grid-color-info th {
    background-color: #a7f3d0 !important;
}

.grid-color-info table {
    border-left: 3px solid #a7f3d0 !important;
}

.grid-color-success th {
    background-color: #c3dafe !important;
}

.grid-color-success table {
    border-left: 3px solid #c3dafe !important;
}

/* #conGrid .custom-dropdown-overlay1 ul li.first a {
    background: #eab308 !important;
}

#conGrid .custom-dropdown-overlay1 ul li.second a {
    background: #ef4444 !important;
}

#conGrid .custom-dropdown-overlay1 ul li.third a {
    background: #22c55e !important;
}

#conGrid .custom-dropdown-overlay1 ul li.fourth a {
    background: #71717a !important;
} */


/* .app-sidebar-logo {
    display: none !important;
} */

.bg-1 {
    background-color: #5C2D91;
}

.cardCustom.bg-1 {
    background: #5C2D91 url(../img/ellipse20.svg) 0 0 no-repeat;
    background-position: 130% -50px !important;
}

.bg-2 {
    background-color: #E20E77;
}

.cardCustom.bg-2 {
    background: #E20E77 url(../img/ellipse21.svg) 0 0 no-repeat;
    background-position: 130% -50px !important;
}

.bg-3 {
    background-color: #FDB913;
}

.cardCustom.bg-3 {
    background: #FDB913 url(../img/ellipse22.svg) 0 0 no-repeat;
    background-position: 130% -50px !important;
}

.bg-4 {
    background-color: #8D64AA;
}

.cardCustom.bg-4 {
    background: #8D64AA url(../img/ellipse23.svg) 0 0 no-repeat;
    background-position: 130% -50px !important;
}


/* Override all button background and border color */
.btn {
    background-color: #003B71 !important; /* Your custom color */
    border-color: #003B71 !important; /* Match the border color */
    color: #fff !important; /* Set text color to white or any other color */
}

/* Hover state for all buttons */
.btn:hover {
    background-color: #003B71 !important; /* Custom hover color */
    border-color: #003B71 !important;
}

/* Active and focus state for all buttons */
.btn:active,
.btn:focus,
.btn:focus-visible {
    background-color: #003B71 !important; /* Custom active color */
    border-color: #003B71 !important;
    box-shadow: none !important; /* Remove shadow if desired */
}

.btn.btn-light i {
    color: #fff !important;
}


.btn.btn-active-light-primary:hover:not(.btn-active), .show>.btn.btn-active-light-primary {
    color: #003B71;
    border-color: #003B71;
    background-color: #003B71 !important;
}

.btn.btn-light-warning:hover:not(.btn-active), .show>.btn.btn-light-warning {
    color: #003B71;
    border-color: #003B71;
    background-color: #003B71 !important;
}

.btn.btn-light-success:hover:not(.btn-active), .show>.btn.btn-light-success {
    color: #003B71;
    border-color: #003B71;
    background-color: #003B71 !important;
}

.btn.btn-light-info:hover:not(.btn-active), .show>.btn.btn-light-info {
    color: #003B71;
    border-color: #003B71;
    background-color: #003B71 !important;
}

.btn.btn-light-primary:hover:not(.btn-active), .show>.btn.btn-light-primary {
    color: #003B71;
    border-color: #003B71;
    background-color: #003B71 !important;
}

.btn.btn-light-danger:hover:not(.btn-active), .show>.btn.btn-light-danger {
    color: #003B71;
    border-color: #003B71;
    background-color: #003B71 !important;
}

.btn.btn-danger:hover:not(.btn-active), .show>.btn.btn-danger {
    color: #003B71;
    border-color: #003B71;
    background-color: #003B71 !important;
}


.active>.page-link, .page-link.active {
    background-color: #003B71;
}

.color-lt {
    color: #01abc8 !important;
}


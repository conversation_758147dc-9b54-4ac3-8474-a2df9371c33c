function FormDataManager(modalIdSelector, modalFormSelector, modalSaveSelector, displayDataSelector, hiddenInputSelector, fields, validationRules, validationMessages, displayFields, modalTextSelector, separator, existingData = [], selectFields = []) {

    this.modalId = $(modalIdSelector);
    this.modalForm = $(modalFormSelector);
    this.modalSave = $(modalSaveSelector);
    this.displayData = $(displayDataSelector);
    this.hiddenInput = $(hiddenInputSelector);
    this.modalText = $(modalTextSelector);
    this.fields = fields;
    this.displayFields = displayFields;
    this.separator = separator;
    this.formDataArray = [];
    this.editingButton = null;
    this.formDataArrayForHiddenInput = [];
    this.selectFields = selectFields;


    this.formDataArray = existingData;
    this.formDataArrayForHiddenInput = existingData.map(data => {
        let hiddenFormData = {};
        fields.forEach(field => hiddenFormData[field] = data[field]);
        return hiddenFormData;
    });

    this.modalForm.validate({
        rules: validationRules,
        messages: validationMessages
    });

    this.bindEvents();

    this.updateDisplayData();
}

FormDataManager.prototype.createDataDisplay = function(data) {
    var self = this;
    var dataAttributes = this.fields.map(field => `data-${field}="${data[field]}"`).join(' ');
    dataAttributes += data.id ? ` data-id="${data.id}"` : '';

    var displayContent = this.displayFields.map(field => {
        if (self.selectFields.includes(field)) {
            var selectElement = $('#' + field);
            var optionElement = selectElement.find('option[value="' + data[field] + '"]');
            var displayText = optionElement.text();
            return displayText;
        } else {
            return data[field];
        }
    }).join(this.separator);

    return `
        <button type="button" class="btn btn-primary edit-btn mt-1" ${dataAttributes}>
            <span class="me-2">${displayContent}</span>
            <span class="badge bg-light text-dark">x</span>
        </button>
    `;
};



FormDataManager.prototype.setModalTextToAdd = function () {
    this.modalText.text('Add');
};

FormDataManager.prototype.setModalTextToEdit = function () {
    this.modalText.text('Edit');
};

FormDataManager.prototype.bindEvents = function () {
    var self = this;

    this.modalSave.click(function () {
        if (self.modalForm.valid()) {
            var formData = {};
            var formDataForHiddenInput = {};
            self.fields.forEach(function (field) {
                // Find the field within the current modal form
                var fieldElement = self.modalForm.find('#' + field);
                if (fieldElement.data('select2')) {
                    // Handling for select2 fields within the modal
                    formData[field] = fieldElement.find('option:selected').text();
                    formDataForHiddenInput[field] = fieldElement.val();
                } else {
                    // Handling for regular fields within the modal
                    formData[field] = fieldElement.val();
                    formDataForHiddenInput[field] = fieldElement.val();
                }
            });

            if (self.editingButton) {
                var indexToUpdate = $(self.editingButton).index();
                var editingId = $(self.editingButton).data('id');
                formData['id'] = editingId;
                formDataForHiddenInput['id'] = editingId;

                self.formDataArray[indexToUpdate] = formData;
                self.formDataArrayForHiddenInput[indexToUpdate] = formDataForHiddenInput;
                self.editingButton = null;
            } else {
                self.formDataArray.push(formData);
                self.formDataArrayForHiddenInput.push(formDataForHiddenInput);
            }
            self.updateDisplayData();
            self.modalId.modal('hide');

            console.log(self.formDataArrayForHiddenInput);
        }
    });


    this.displayData.on('click', '.edit-btn', function (e) {
        if ($(e.target).hasClass('badge')) {
            var indexToRemove = $(this).index();
            self.formDataArray.splice(indexToRemove, 1);
            self.updateDisplayData();
        } else {
            self.editingButton = this;
            var indexToEdit = $(this).index();
            var dataToEdit = self.formDataArrayForHiddenInput[indexToEdit];
            self.fields.forEach(function (field) {
                var fieldElement = $('#' + field);
                var valueToSet = dataToEdit[field];
                if (fieldElement.data('select2')) {
                    fieldElement.val(valueToSet).trigger('change');
                } else {
                    fieldElement.val(valueToSet);
                }
            });
            self.setModalTextToEdit();
            self.modalId.modal('show');
        }

    });

    this.modalId.on('hidden.bs.modal', function () {
        self.modalForm[0].reset();
        self.modalForm.validate().resetForm();
        self.updateDisplayData();
        self.setModalTextToAdd();
    });
};

FormDataManager.prototype.updateDisplayData = function () {
    var self = this;
    this.displayData.empty();
    var hiddenDataArray = [];

    this.formDataArray.forEach(function (data, index) {
        var display = self.createDataDisplay(data);
        self.displayData.append(display);
        hiddenDataArray.push(self.formDataArrayForHiddenInput[index]);
    });

    this.hiddenInput.val(JSON.stringify(hiddenDataArray));
};

*, ::before, ::after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x:  ;
    --tw-pan-y:  ;
    --tw-pinch-zoom:  ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position:  ;
    --tw-gradient-via-position:  ;
    --tw-gradient-to-position:  ;
    --tw-ordinal:  ;
    --tw-slashed-zero:  ;
    --tw-numeric-figure:  ;
    --tw-numeric-spacing:  ;
    --tw-numeric-fraction:  ;
    --tw-ring-inset:  ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(63 131 248 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur:  ;
    --tw-brightness:  ;
    --tw-contrast:  ;
    --tw-grayscale:  ;
    --tw-hue-rotate:  ;
    --tw-invert:  ;
    --tw-saturate:  ;
    --tw-sepia:  ;
    --tw-drop-shadow:  ;
    --tw-backdrop-blur:  ;
    --tw-backdrop-brightness:  ;
    --tw-backdrop-contrast:  ;
    --tw-backdrop-grayscale:  ;
    --tw-backdrop-hue-rotate:  ;
    --tw-backdrop-invert:  ;
    --tw-backdrop-opacity:  ;
    --tw-backdrop-saturate:  ;
    --tw-backdrop-sepia:  ;
    --tw-contain-size:  ;
    --tw-contain-layout:  ;
    --tw-contain-paint:  ;
    --tw-contain-style:  ;
  }

  ::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x:  ;
    --tw-pan-y:  ;
    --tw-pinch-zoom:  ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position:  ;
    --tw-gradient-via-position:  ;
    --tw-gradient-to-position:  ;
    --tw-ordinal:  ;
    --tw-slashed-zero:  ;
    --tw-numeric-figure:  ;
    --tw-numeric-spacing:  ;
    --tw-numeric-fraction:  ;
    --tw-ring-inset:  ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(63 131 248 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur:  ;
    --tw-brightness:  ;
    --tw-contrast:  ;
    --tw-grayscale:  ;
    --tw-hue-rotate:  ;
    --tw-invert:  ;
    --tw-saturate:  ;
    --tw-sepia:  ;
    --tw-drop-shadow:  ;
    --tw-backdrop-blur:  ;
    --tw-backdrop-brightness:  ;
    --tw-backdrop-contrast:  ;
    --tw-backdrop-grayscale:  ;
    --tw-backdrop-hue-rotate:  ;
    --tw-backdrop-invert:  ;
    --tw-backdrop-opacity:  ;
    --tw-backdrop-saturate:  ;
    --tw-backdrop-sepia:  ;
    --tw-contain-size:  ;
    --tw-contain-layout:  ;
    --tw-contain-paint:  ;
    --tw-contain-style:  ;
  }

  /*
  ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
  */

  /*
  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
  2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
  */

  *,
  ::before,
  ::after {
    box-sizing: border-box;
    /* 1 */
    border-width: 0;
    /* 2 */
    border-style: solid;
    /* 2 */
    border-color: #E5E7EB;
    /* 2 */
  }

  ::before,
  ::after {
    --tw-content: '';
  }

  /*
  1. Use a consistent sensible line-height in all browsers.
  2. Prevent adjustments of font size after orientation changes in iOS.
  3. Use a more readable tab size.
  4. Use the user's configured `sans` font-family by default.
  5. Use the user's configured `sans` font-feature-settings by default.
  6. Use the user's configured `sans` font-variation-settings by default.
  7. Disable tap highlights on iOS
  */

  html,
  :host {
    line-height: 1.5;
    /* 1 */
    -webkit-text-size-adjust: 100%;
    /* 2 */
    -moz-tab-size: 4;
    /* 3 */
    -o-tab-size: 4;
       tab-size: 4;
    /* 3 */
    font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    /* 4 */
    font-feature-settings: normal;
    /* 5 */
    font-variation-settings: normal;
    /* 6 */
    -webkit-tap-highlight-color: transparent;
    /* 7 */
  }

  /*
  1. Remove the margin in all browsers.
  2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
  */

  body {
    margin: 0;
    /* 1 */
    line-height: inherit;
    /* 2 */
  }

  /*
  1. Add the correct height in Firefox.
  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
  3. Ensure horizontal rules are visible by default.
  */

  hr {
    height: 0;
    /* 1 */
    color: inherit;
    /* 2 */
    border-top-width: 1px;
    /* 3 */
  }

  /*
  Add the correct text decoration in Chrome, Edge, and Safari.
  */

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
            text-decoration: underline dotted;
  }

  /*
  Remove the default font size and weight for headings.
  */

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  /*
  Reset links to optimize for opt-in styling instead of opt-out.
  */

  a {
    color: inherit;
    text-decoration: inherit;
  }

  /*
  Add the correct font weight in Edge and Safari.
  */

  b,
  strong {
    font-weight: bolder;
  }

  /*
  1. Use the user's configured `mono` font-family by default.
  2. Use the user's configured `mono` font-feature-settings by default.
  3. Use the user's configured `mono` font-variation-settings by default.
  4. Correct the odd `em` font sizing in all browsers.
  */

  code,
  kbd,
  samp,
  pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    /* 1 */
    font-feature-settings: normal;
    /* 2 */
    font-variation-settings: normal;
    /* 3 */
    font-size: 1em;
    /* 4 */
  }

  /*
  Add the correct font size in all browsers.
  */

  small {
    font-size: 80%;
  }

  /*
  Prevent `sub` and `sup` elements from affecting the line height in all browsers.
  */

  sub,
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sub {
    bottom: -0.25em;
  }

  sup {
    top: -0.5em;
  }

  /*
  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
  3. Remove gaps between table borders by default.
  */

  table {
    text-indent: 0;
    /* 1 */
    border-color: inherit;
    /* 2 */
    border-collapse: collapse;
    /* 3 */
  }

  /*
  1. Change the font styles in all browsers.
  2. Remove the margin in Firefox and Safari.
  3. Remove default padding in all browsers.
  */

  button,
  input,
  optgroup,
  select,
  textarea {
    font-family: inherit;
    /* 1 */
    font-feature-settings: inherit;
    /* 1 */
    font-variation-settings: inherit;
    /* 1 */
    font-size: 100%;
    /* 1 */
    font-weight: inherit;
    /* 1 */
    line-height: inherit;
    /* 1 */
    letter-spacing: inherit;
    /* 1 */
    color: inherit;
    /* 1 */
    margin: 0;
    /* 2 */
    padding: 0;
    /* 3 */
  }

  /*
  Remove the inheritance of text transform in Edge and Firefox.
  */

  button,
  select {
    text-transform: none;
  }

  /*
  1. Correct the inability to style clickable types in iOS and Safari.
  2. Remove default button styles.
  */

  button,
  input:where([type='button']),
  input:where([type='reset']),
  input:where([type='submit']) {
    -webkit-appearance: button;
    /* 1 */
    background-color: transparent;
    /* 2 */
    background-image: none;
    /* 2 */
  }

  /*
  Use the modern Firefox focus style for all focusable elements.
  */

  :-moz-focusring {
    outline: auto;
  }

  /*
  Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
  */

  :-moz-ui-invalid {
    box-shadow: none;
  }

  /*
  Add the correct vertical alignment in Chrome and Firefox.
  */

  progress {
    vertical-align: baseline;
  }

  /*
  Correct the cursor style of increment and decrement buttons in Safari.
  */

  ::-webkit-inner-spin-button,
  ::-webkit-outer-spin-button {
    height: auto;
  }

  /*
  1. Correct the odd appearance in Chrome and Safari.
  2. Correct the outline style in Safari.
  */

  [type='search'] {
    -webkit-appearance: textfield;
    /* 1 */
    outline-offset: -2px;
    /* 2 */
  }

  /*
  Remove the inner padding in Chrome and Safari on macOS.
  */

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  /*
  1. Correct the inability to style clickable types in iOS and Safari.
  2. Change font properties to `inherit` in Safari.
  */

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    /* 1 */
    font: inherit;
    /* 2 */
  }

  /*
  Add the correct display in Chrome and Safari.
  */

  summary {
    display: list-item;
  }

  /*
  Removes the default spacing and border for appropriate elements.
  */

  blockquote,
  dl,
  dd,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  hr,
  figure,
  p,
  pre {
    margin: 0;
  }

  fieldset {
    margin: 0;
    padding: 0;
  }

  legend {
    padding: 0;
  }

  ol,
  ul,
  menu {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  /*
  Reset default styling for dialogs.
  */

  dialog {
    padding: 0;
  }

  /*
  Prevent resizing textareas horizontally by default.
  */

  textarea {
    resize: vertical;
  }

  /*
  1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
  2. Set the default placeholder color to the user's configured gray 400 color.
  */

  input::-moz-placeholder, textarea::-moz-placeholder {
    opacity: 1;
    /* 1 */
    color: #9CA3AF;
    /* 2 */
  }

  input::placeholder,
  textarea::placeholder {
    opacity: 1;
    /* 1 */
    color: #9CA3AF;
    /* 2 */
  }

  /*
  Set the default cursor for buttons.
  */

  button,
  [role="button"] {
    cursor: pointer;
  }

  /*
  Make sure disabled buttons don't get the pointer cursor.
  */

  :disabled {
    cursor: default;
  }

  /*
  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
     This can trigger a poorly considered lint error in some tools but is included by design.
  */

  img,
  svg,
  video,
  canvas,
  audio,
  iframe,
  embed,
  object {
    display: block;
    /* 1 */
    vertical-align: middle;
    /* 2 */
  }

  /*
  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
  */

  img,
  video {
    max-width: 100%;
    height: auto;
  }

  /* Make elements with the HTML hidden attribute stay hidden by default */

  [hidden]:where(:not([hidden="until-found"])) {
    display: none;
  }

  .tooltip-arrow,.tooltip-arrow:before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
  }

  .tooltip-arrow {
    visibility: hidden;
  }

  .tooltip-arrow:before {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
  }

  [data-tooltip-style^='light'] + .tooltip > .tooltip-arrow:before {
    border-style: solid;
    border-color: #e5e7eb;
  }

  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='top'] > .tooltip-arrow:before {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }

  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='right'] > .tooltip-arrow:before {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }

  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow:before {
    border-top-width: 1px;
    border-left-width: 1px;
  }

  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='left'] > .tooltip-arrow:before {
    border-top-width: 1px;
    border-right-width: 1px;
  }

  .tooltip[data-popper-placement^='top'] > .tooltip-arrow {
    bottom: -4px;
  }

  .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow {
    top: -4px;
  }

  .tooltip[data-popper-placement^='left'] > .tooltip-arrow {
    right: -4px;
  }

  .tooltip[data-popper-placement^='right'] > .tooltip-arrow {
    left: -4px;
  }

  .tooltip.invisible > .tooltip-arrow:before {
    visibility: hidden;
  }

  [data-popper-arrow],[data-popper-arrow]:before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
  }

  [data-popper-arrow] {
    visibility: hidden;
  }

  [data-popper-arrow]:before {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
  }

  [data-popper-arrow]:after {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
    position: absolute;
    width: 9px;
    height: 9px;
    background: inherit;
  }

  [role="tooltip"] > [data-popper-arrow]:before {
    border-style: solid;
    border-color: #e5e7eb;
  }

  .dark [role="tooltip"] > [data-popper-arrow]:before {
    border-style: solid;
    border-color: #4b5563;
  }

  [role="tooltip"] > [data-popper-arrow]:after {
    border-style: solid;
    border-color: #e5e7eb;
  }

  .dark [role="tooltip"] > [data-popper-arrow]:after {
    border-style: solid;
    border-color: #4b5563;
  }

  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:before {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:after {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:before {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:after {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:before {
    border-top-width: 1px;
    border-left-width: 1px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:after {
    border-top-width: 1px;
    border-left-width: 1px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:before {
    border-top-width: 1px;
    border-right-width: 1px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:after {
    border-top-width: 1px;
    border-right-width: 1px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow] {
    bottom: -5px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow] {
    top: -5px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow] {
    right: -5px;
  }

  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow] {
    left: -5px;
  }

  [role="tooltip"].invisible > [data-popper-arrow]:before {
    visibility: hidden;
  }

  [role="tooltip"].invisible > [data-popper-arrow]:after {
    visibility: hidden;
  }

  [type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
    background-color: #fff;
    border-color: #6B7280;
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
  }

  [type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #1C64F2;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    border-color: #1C64F2;
  }

  input::-moz-placeholder, textarea::-moz-placeholder {
    color: #6B7280;
    opacity: 1;
  }

  input::placeholder,textarea::placeholder {
    color: #6B7280;
    opacity: 1;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  input[type="time"]::-webkit-calendar-picker-indicator {
    background: none;
  }

  select:not([size]) {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 10 6'%3e %3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 1 4 4 4-4'/%3e %3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 0.75em 0.75em;
    padding-right: 2.5rem;
    -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
  }

  :is([dir=rtl]) select:not([size]) {
    background-position: left 0.75rem center;
    padding-right: 0.75rem;
    padding-left: 0;
  }

  [multiple] {
    background-image: initial;
    background-position: initial;
    background-repeat: unset;
    background-size: initial;
    padding-right: 0.75rem;
    -webkit-print-color-adjust: unset;
            print-color-adjust: unset;
  }

  [type='checkbox'],[type='radio'] {
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
    padding: 0;
    -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    -webkit-user-select: none;
       -moz-user-select: none;
            user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: #1C64F2;
    background-color: #fff;
    border-color: #6B7280;
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
  }

  [type='checkbox'] {
    border-radius: 0px;
  }

  [type='radio'] {
    border-radius: 100%;
  }

  [type='checkbox']:focus,[type='radio']:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #1C64F2;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  [type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked {
    border-color: transparent;
    background-color: currentColor;
    background-size: 0.55em 0.55em;
    background-position: center;
    background-repeat: no-repeat;
  }

  [type='checkbox']:checked {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M1 5.917 5.724 10.5 15 1.5'/%3e %3c/svg%3e");
    background-repeat: no-repeat;
    background-size: 0.55em 0.55em;
    -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
  }

  [type='radio']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
    background-size: 1em 1em;
  }

  .dark [type='radio']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
    background-size: 1em 1em;
  }

  [type='checkbox']:indeterminate {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3e %3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M0.5 6h14'/%3e %3c/svg%3e");
    background-color: currentColor;
    border-color: transparent;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.55em 0.55em;
    -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
  }

  [type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
    border-color: transparent;
    background-color: currentColor;
  }

  [type='file'] {
    background: unset;
    border-color: inherit;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-size: unset;
    line-height: inherit;
  }

  [type='file']:focus {
    outline: 1px auto inherit;
  }

  input[type=file]::file-selector-button {
    color: white;
    background: #1F2937;
    border: 0;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
    padding-left: 2rem;
    padding-right: 1rem;
    margin-inline-start: -1rem;
    margin-inline-end: 1rem;
  }

  input[type=file]::file-selector-button:hover {
    background: #374151;
  }

  :is([dir=rtl]) input[type=file]::file-selector-button {
    padding-right: 2rem;
    padding-left: 1rem;
  }

  .dark input[type=file]::file-selector-button {
    color: white;
    background: #4B5563;
  }

  .dark input[type=file]::file-selector-button:hover {
    background: #6B7280;
  }

  input[type="range"]::-webkit-slider-thumb {
    height: 1.25rem;
    width: 1.25rem;
    background: #1C64F2;
    border-radius: 9999px;
    border: 0;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
  }

  input[type="range"]:disabled::-webkit-slider-thumb {
    background: #9CA3AF;
  }

  .dark input[type="range"]:disabled::-webkit-slider-thumb {
    background: #6B7280;
  }

  input[type="range"]:focus::-webkit-slider-thumb {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    --tw-ring-opacity: 1px;
    --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
  }

  input[type="range"]::-moz-range-thumb {
    height: 1.25rem;
    width: 1.25rem;
    background: #1C64F2;
    border-radius: 9999px;
    border: 0;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
  }

  input[type="range"]:disabled::-moz-range-thumb {
    background: #9CA3AF;
  }

  .dark input[type="range"]:disabled::-moz-range-thumb {
    background: #6B7280;
  }

  input[type="range"]::-moz-range-progress {
    background: #3F83F8;
  }

  input[type="range"]::-ms-fill-lower {
    background: #3F83F8;
  }

  .toggle-bg:after {
    content: "";
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    background: white;
    border-color: #D1D5DB;
    border-width: 1px;
    border-radius: 9999px;
    height: 1.25rem;
    width: 1.25rem;
    transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;
    transition-duration: .15s;
    box-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  }

  input:checked + .toggle-bg:after {
    transform: translateX(100%);;
    border-color: white;
  }

  input:checked + .toggle-bg {
    background: #1C64F2;
    border-color: #1C64F2;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 640px) {
    .container {
      max-width: 640px;
    }
  }

  @media (min-width: 768px) {
    .container {
      max-width: 768px;
    }
  }

  @media (min-width: 1024px) {
    .container {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .container {
      max-width: 1280px;
    }
  }

  @media (min-width: 1536px) {
    .container {
      max-width: 1536px;
    }
  }

  .apexcharts-canvas .apexcharts-tooltip {
    background-color: white;
    color: #6B7280;
    border: 0 !important;
    border-radius: 0.25rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .dark .apexcharts-canvas .apexcharts-tooltip {
    background-color: #374151;
    color: #9CA3AF;
    border-color: transparent;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-title {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
    margin-bottom: 0.75rem;
    background-color: #F3F4F6;
    border-bottom-color: #E5E7EB;
    font-size: 0.875rem !important;
    font-weight: 400;
    color: #6B7280;
  }

  .dark .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-title {
    background-color: #4B5563;
    border-color: #6B7280;
    color: #9CA3AF;
  }

  .apexcharts-canvas .apexcharts-xaxistooltip {
    color: #6B7280;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
    border-color: transparent;
    background-color: white;
    border-radius: 0.25rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .dark .apexcharts-canvas .apexcharts-xaxistooltip {
    color: #9CA3AF;
    background-color: #374151;
  }

  .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
    color: #6B7280;
    font-size: 0.875rem;
  }

  .dark .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
    color: #9CA3AF;
  }

  .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
    color: #111827;
    font-size: 0.875rem;
  }

  :is([dir=rtl]) .apexcharts-tooltip .apexcharts-tooltip-marker {
    margin-right: 0px;
    margin-left: e;
  }

  .dark .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
    color: white;
  }

  .apexcharts-canvas .apexcharts-xaxistooltip-text {
    font-weight: 400;
    font-size: 0.875rem !important;
  }

  .apexcharts-canvas .apexcharts-xaxistooltip:after, .apexcharts-canvas .apexcharts-xaxistooltip:before {
    border-bottom-color: white;
  }

  .apexcharts-canvas .apexcharts-xaxistooltip:after {
    border-width: 8px;
    margin-left: -8px;
  }

  .apexcharts-canvas .apexcharts-xaxistooltip:before {
    border-width: 10px;
    margin-left: -10px;
  }

  .dark .apexcharts-canvas .apexcharts-xaxistooltip:after, .dark .apexcharts-canvas .apexcharts-xaxistooltip:before {
    border-bottom-color: #374151;
  }

  .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-y-group {
    padding: 0;
  }

  .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-bottom: 0.75rem;
    background-color: white !important;
    color: #6B7280 !important;
  }

  .dark .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active {
    background-color: #374151 !important;
    color: #9CA3AF !important;
  }

  .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active:first-of-type {
    padding-top: 0.75rem;
  }

  .apexcharts-canvas .apexcharts-legend {
    padding: 0 !important;
  }

  .apexcharts-canvas .apexcharts-legend-text {
    font-size: 0.75rem;
    font-weight: 500 !important;
    padding-left: 1.25rem;
    color: #6B7280 !important;
  }

  :is([dir=rtl]) .apexcharts-canvas .apexcharts-legend-text {
    padding-right: 0.5rem;
  }

  .apexcharts-canvas .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
    color: #111827 !important;
  }

  .dark .apexcharts-canvas .apexcharts-legend-text {
    color: #9CA3AF !important;
  }

  .dark .apexcharts-canvas .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
    color: white !important;
  }

  .apexcharts-canvas .apexcharts-legend-series {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem !important;
    display: flex;
    align-items: center;
  }

  .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
    fill: #111827 !important;
    font-size: 1.875rem;
    font-weight: 700;
  }

  .dark .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
    fill: white !important;
  }

  .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
    fill: #6B7280 !important;
    font-size: 1rem;
    font-weight: 400;
  }

  .dark .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
    fill: #9CA3AF !important;
  }

  .apexcharts-canvas .apexcharts-datalabels .apexcharts-text.apexcharts-pie-label {
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-shadow: none !important;
    filter: none !important;
  }

  .apexcharts-gridline, .apexcharts-xcrosshairs, .apexcharts-ycrosshairs {
    stroke: #E5E7EB !important;
  }

  .dark .apexcharts-gridline, .dark .apexcharts-xcrosshairs, .dark .apexcharts-ycrosshairs {
    stroke: #374151 !important;
  }

  .visible {
    visibility: visible;
  }

  .invisible {
    visibility: hidden;
  }

  .collapse {
    visibility: collapse;
  }

  .static {
    position: static;
  }

  .fixed {
    position: fixed;
  }

  .absolute {
    position: absolute;
  }

  .relative {
    position: relative;
  }

  .inset-0 {
    inset: 0px;
  }

  .bottom-0 {
    bottom: 0px;
  }

  .bottom-\[60px\] {
    bottom: 60px;
  }

  .left-0 {
    left: 0px;
  }

  .right-0 {
    right: 0px;
  }

  .top-0 {
    top: 0px;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .my-10 {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
  }

  .mb-1 {
    margin-bottom: 0.25rem;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  .mb-4 {
    margin-bottom: 1rem;
  }

  .mr-2 {
    margin-right: 0.5rem;
  }

  .mt-1 {
    margin-top: 0.25rem;
  }

  .mt-10 {
    margin-top: 2.5rem;
  }

  .mt-2 {
    margin-top: 0.5rem;
  }

  .mt-20 {
    margin-top: 5rem;
  }

  .mt-3 {
    margin-top: 0.75rem;
  }

  .mt-7 {
    margin-top: 1.75rem;
  }

  .mt-8 {
    margin-top: 2rem;
  }

  .mr-1 {
    margin-right: 0.25rem;
  }

  .block {
    display: block;
  }

  .inline-block {
    display: inline-block;
  }

  .inline {
    display: inline;
  }

  .flex {
    display: flex;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .h-28 {
    height: 7rem;
  }

  .h-4 {
    height: 1rem;
  }

  .h-6 {
    height: 1.5rem;
  }

  .h-9 {
    height: 2.25rem;
  }

  .h-auto {
    height: auto;
  }

  .h-full {
    height: 100%;
  }

  .h-5 {
    height: 1.25rem;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-16 {
    width: 4rem;
  }

  .w-4 {
    width: 1rem;
  }

  .w-64 {
    width: 16rem;
  }

  .w-\[20px\] {
    width: 20px;
  }

  .w-\[30px\] {
    width: 30px;
  }

  .w-full {
    width: 100%;
  }

  .w-5 {
    width: 1.25rem;
  }

  .w-8 {
    width: 2rem;
  }

  .max-w-\[250px\] {
    max-width: 250px;
  }

  .max-w-\[500px\] {
    max-width: 500px;
  }

  .max-w-\[600px\] {
    max-width: 600px;
  }

  .flex-1 {
    flex: 1 1 0%;
  }

  .flex-shrink {
    flex-shrink: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .-translate-y-full {
    --tw-translate-y: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .translate-x-full {
    --tw-translate-x: 100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .translate-y-full {
    --tw-translate-y: 100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .rotate-180 {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .transform-none {
    transform: none;
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .list-disc {
    list-style-type: disc;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-center {
    align-items: center;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-items-end {
    justify-items: end;
  }

  .gap-2 {
    gap: 0.5rem;
  }

  .gap-4 {
    gap: 1rem;
  }

  .space-x-10 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2.5rem * var(--tw-space-x-reverse));
    margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
  }

  .space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
  }

  .space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .rounded-full {
    border-radius: 9999px;
  }

  .rounded-lg {
    border-radius: 0.5rem;
  }

  .rounded-e-lg {
    border-start-end-radius: 0.5rem;
    border-end-end-radius: 0.5rem;
  }

  .rounded-l-lg {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
  }

  .rounded-r-lg {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
  }

  .rounded-s-lg {
    border-start-start-radius: 0.5rem;
    border-end-start-radius: 0.5rem;
  }

  .border {
    border-width: 1px;
  }

  .border-0 {
    border-width: 0px;
  }

  .border-blue-600 {
    --tw-border-opacity: 1;
    border-color: rgb(28 100 242 / var(--tw-border-opacity, 1));
  }

  .border-blue-700 {
    --tw-border-opacity: 1;
    border-color: rgb(26 86 219 / var(--tw-border-opacity, 1));
  }

  .border-gray-100 {
    --tw-border-opacity: 1;
    border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
  }

  .border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  }

  .\!bg-gray-50 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1)) !important;
  }

  .bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  }

  .bg-blue-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(26 86 219 / var(--tw-bg-opacity, 1));
  }

  .bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  }

  .bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
  }

  .bg-gray-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
  }

  .bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
  }

  .bg-gray-900\/50 {
    background-color: rgb(17 24 39 / 0.5);
  }

  .bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  }

  .bg-white\/50 {
    background-color: rgb(255 255 255 / 0.5);
  }

  .bg-opacity-30 {
    --tw-bg-opacity: 0.3;
  }

  .bg-opacity-70 {
    --tw-bg-opacity: 0.7;
  }

  .bg-opacity-5 {
    --tw-bg-opacity: 0.05;
  }

  .bg-\[url\(\'\.\/img\/bg1\.jpg\'\)\] {
    background-image: url('./img/bg1.jpg');
  }

  .bg-\[url\(\'\.\/img\/bg2\.jpg\'\)\] {
    background-image: url('./img/bg2.jpg');
  }

  .bg-\[url\(\'\.\/img\/bg3\.jpg\'\)\] {
    background-image: url('./img/bg3.jpg');
  }

  .bg-\[url\(\'\.\/img\/bg4\.jpg\'\)\] {
    background-image: url('./img/bg4.jpg');
  }

  .bg-\[url\(\'\.\/img\/bg5\.jpg\'\)\] {
    background-image: url('./img/bg5.jpg');
  }

  .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
  }

  .from-\[\#2e2b77\] {
    --tw-gradient-from: #2e2b77 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(46 43 119 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .from-\[\#fcef50\] {
    --tw-gradient-from: #fcef50 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(252 239 80 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .from-yellow-400 {
    --tw-gradient-from: #E3A008 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(227 160 8 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .via-orange-500 {
    --tw-gradient-to: rgb(255 90 31 / 0)  var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), #FF5A1F var(--tw-gradient-via-position), var(--tw-gradient-to);
  }

  .to-\[\#70f2e0\] {
    --tw-gradient-to: #70f2e0 var(--tw-gradient-to-position);
  }

  .to-\[\#98c254\] {
    --tw-gradient-to: #98c254 var(--tw-gradient-to-position);
  }

  .to-red-500 {
    --tw-gradient-to: #F05252 var(--tw-gradient-to-position);
  }

  .bg-cover {
    background-size: cover;
  }

  .bg-center {
    background-position: center;
  }

  .bg-top {
    background-position: top;
  }

  .p-1 {
    padding: 0.25rem;
  }

  .p-2\.5 {
    padding: 0.625rem;
  }

  .p-4 {
    padding: 1rem;
  }

  .p-\[4px\] {
    padding: 4px;
  }

  .px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .pl-5 {
    padding-left: 1.25rem;
  }

  .pr-4 {
    padding-right: 1rem;
  }

  .pt-2 {
    padding-top: 0.5rem;
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }

  .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .font-light {
    font-weight: 300;
  }

  .font-medium {
    font-weight: 500;
  }

  .font-normal {
    font-weight: 400;
  }

  .font-semibold {
    font-weight: 600;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .leading-6 {
    line-height: 1.5rem;
  }

  .leading-9 {
    line-height: 2.25rem;
  }

  .leading-relaxed {
    line-height: 1.625;
  }

  .leading-snug {
    line-height: 1.375;
  }

  .text-blue-600 {
    --tw-text-opacity: 1;
    color: rgb(28 100 242 / var(--tw-text-opacity, 1));
  }

  .text-customgray {
    --tw-text-opacity: 1;
    color: rgb(104 103 102 / var(--tw-text-opacity, 1));
  }

  .text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }

  .text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  }

  .text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
  }

  .text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
  }

  .text-gray-800 {
    --tw-text-opacity: 1;
    color: rgb(31 41 55 / var(--tw-text-opacity, 1));
  }

  .text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
  }

  .text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .outline {
    outline-style: solid;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }

  .filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
  }

  .transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }

  body {
    font-family: "Baskervville", serif !important;
  }

  .container-inner {
    max-width: 1060px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-top: 200px;
    padding-bottom: 80px;
  }

  .hover\:border-gray-300:hover {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  }

  .hover\:bg-blue-800:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(30 66 159 / var(--tw-bg-opacity, 1));
  }

  .hover\:bg-gray-100:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
  }

  .hover\:bg-white:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  }

  .hover\:text-blue-600:hover {
    --tw-text-opacity: 1;
    color: rgb(28 100 242 / var(--tw-text-opacity, 1));
  }

  .hover\:text-gray-600:hover {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
  }

  .hover\:text-gray-900:hover {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
  }

  .hover\:underline:hover {
    text-decoration-line: underline;
  }

  .focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  .focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  }

  .focus\:ring-4:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  }

  .focus\:ring-blue-300:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity, 1));
  }

  .focus\:ring-gray-200:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));
  }

  .dark\:border-blue-500:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(63 131 248 / var(--tw-border-opacity, 1));
  }

  .dark\:border-gray-600:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
  }

  .dark\:border-gray-700:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  }

  .dark\:border-transparent:is(.dark *) {
    border-color: transparent;
  }

  .dark\:\!bg-gray-700:is(.dark *) {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1)) !important;
  }

  .dark\:bg-blue-600:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(28 100 242 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-600:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-700:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-800:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-800\/50:is(.dark *) {
    background-color: rgb(31 41 55 / 0.5);
  }

  .dark\:bg-gray-900\/80:is(.dark *) {
    background-color: rgb(17 24 39 / 0.8);
  }

  .dark\:text-blue-500:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(63 131 248 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-400:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-500:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  }

  .dark\:text-white:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:bg-blue-700:hover:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(26 86 219 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-gray-600:hover:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-gray-800:hover:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:text-blue-500:hover:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(63 131 248 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-gray-300:hover:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-white:hover:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  @media (min-width: 640px) {
    .sm\:flex-row {
      flex-direction: row;
    }

    .sm\:px-6 {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }

    .sm\:text-3xl {
      font-size: 1.875rem;
      line-height: 2.25rem;
    }
  }

  @media (min-width: 768px) {
    .md\:mb-0 {
      margin-bottom: 0px;
    }

    .md\:flex {
      display: flex;
    }

    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .md\:flex-row {
      flex-direction: row;
    }

    .md\:text-right {
      text-align: right;
    }

    .md\:text-3xl {
      font-size: 1.875rem;
      line-height: 2.25rem;
    }

    .md\:text-4xl {
      font-size: 2.25rem;
      line-height: 2.5rem;
    }

    .md\:text-base {
      font-size: 1rem;
      line-height: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .lg\:w-1\/2 {
      width: 50%;
    }

    .lg\:justify-end {
      justify-content: flex-end;
    }

    .lg\:px-24 {
      padding-left: 6rem;
      padding-right: 6rem;
    }

    .lg\:text-right {
      text-align: right;
    }
  }

  .rtl\:rotate-180:where([dir="rtl"], [dir="rtl"] *) {
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .rtl\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

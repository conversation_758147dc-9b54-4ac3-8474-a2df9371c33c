#menu-list li:last-child,
#menu-list-1 li:last-child,
#menu-list-2 li:last-child,
#social-list li:last-child {
    margin-bottom: 0;
}

#menu-list,
#menu-list-1,
#menu-list-2,
#social-list {
    min-height: 19rem;
    max-height: 19rem;
    overflow-y: auto;
}

#carousel-list {
    min-height: 45rem;
    max-height: 45rem;
    overflow-y: auto;
}

#equipment-list {
    min-height: 19rem;
    max-height: 19rem;
    overflow-y: auto;
}

#securite-list {
    min-height: 19rem;
    max-height: 19rem;
    overflow-y: auto;
}

#galerie-list {
    min-height: 40rem;
    max-height: 40rem;
    overflow-y: auto;
}


#drawer-content {
    height: calc(113vh - 265px);
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
}

#drawer {
    width: 20% !important;
}

#page-content {
    width: 80% !important;
}


#page-content, #drawer {
    height: calc(100vh);
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
}



.rte-modern.rte-desktop.rte-toolbar-default {
    min-width: 100px !important;
}

.ck-editor__editable {
    height: 375px;
    max-height: 375px;
    overflow-y: auto;
}



.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}


.mb-4 {
    margin-bottom: 1rem;
}

#drawer, #extralarge-modal{
    font-family: sans-serif;;
}

.no-scrollbar::-webkit-scrollbar {
    display: none;
}

.no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

﻿//German , Deutsch
RTE_DefaultConfig.text_language = "sprache";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "stornieren";	//"Cancel"
RTE_DefaultConfig.text_normal = "normalen";	//"Normal"
RTE_DefaultConfig.text_h1 = "Überschrift 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Überschrift 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Überschrift 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Überschrift 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Überschrift 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Überschrift 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Überschrift 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "schließen";	//"Close"
RTE_DefaultConfig.text_bold = "fett";	//"Bold"
RTE_DefaultConfig.text_italic = "kursiv";	//"Italic"
RTE_DefaultConfig.text_underline = "unterstreichen";	//"Underline"
RTE_DefaultConfig.text_strike = "Strike Line";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "hochgestellt";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Upper Case";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Kleinbuchstaben";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Entfernen Format";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Link einfügen";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Offener Link";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Link bearbeiten";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Entfernen Link";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Linienhöhe";	//"Line Height"
RTE_DefaultConfig.text_indent = "einrücken";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Block-Zitat";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Geordnete Liste";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Ungeordnete Liste";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Einfügen der horizontalen Regel";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Datum einfügen";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Tabelle einfügen";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Einfügen von Bild";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Einfügen von Videos";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Code einfügen";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF erstellen";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Einfügen von Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Sonderzeichen";	//"Special characters"
RTE_DefaultConfig.text_characters = "zeichen";	//"Characters"
RTE_DefaultConfig.text_fontname = "schrift";	//"Font"
RTE_DefaultConfig.text_fontsize = "große";	//"Size"
RTE_DefaultConfig.text_forecolor = "Textfarbe";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Rückenfarbe";	//"Back Color"
RTE_DefaultConfig.text_justify = "rechtfertigen";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Justify Left";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Justify Right";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Justify Center";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Justify Full";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Justify None";	//"Justify None"
RTE_DefaultConfig.text_delete = "streichen";	//"Delete"
RTE_DefaultConfig.text_save = "Datei speichern";	//"Save file"
RTE_DefaultConfig.text_selectall = "Wählen Sie Alle";	//"Select All"
RTE_DefaultConfig.text_code = "HTML-Code";	//"HTML Code"
RTE_DefaultConfig.text_preview = "vorschau";	//"Preview"
RTE_DefaultConfig.text_print = "abzug";	//"Print"
RTE_DefaultConfig.text_undo = "ungeschehen machen";	//"Undo"
RTE_DefaultConfig.text_redo = "wiederholen";	//"Redo"
RTE_DefaultConfig.text_more = "Mehr...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Neuer Doc";	//"New Doc"
RTE_DefaultConfig.text_help = "helfen";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Fit to Window";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Beenden Sie den Vollbildmodus";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Bild-Editor";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Bildstile";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Inline-Stile";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Absatzstile";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Linkstile";	//"Link Styles"
RTE_DefaultConfig.text_link = "verbindung";	//"Link"
RTE_DefaultConfig.text_style = "stile";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css-Klassen";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Nach Url";	//"By Url"
RTE_DefaultConfig.text_upload = "uploaden";	//"Upload"
RTE_DefaultConfig.text_size = "große";	//"Size"
RTE_DefaultConfig.text_text = "text";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Öffnen in neuem Tab";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "einsetzen";	//"Insert"
RTE_DefaultConfig.text_update = "aktualisieren";	//"Update"
RTE_DefaultConfig.text_find = "Find&Replace";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "suchen";	//"Find"
RTE_DefaultConfig.text_replacewith = "ersetzen";	//"Replace"
RTE_DefaultConfig.text_findnext = "andere";	//"Next"
RTE_DefaultConfig.text_replaceonce = "ersetzen";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Ersetzen Sie alle";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Match Case";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Match Word";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Move Down";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Move Up";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Autogröße";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% Breite";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% Breite";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% Breite";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% Breite";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Set-Größe";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt-Text";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "rechtfertigen";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Bildunterschrift";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Zusammenführen von Zellen";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Split Cells Vertikal";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Split Cells Horizontal";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Zelltextfarbe";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Zelle zurück Farbe";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Zeile oben einfügen";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Zeile unten einfügen";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Spalte links einfügen";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Spalte rechts einfügen";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Spalte löschen";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Zeile löschen";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Tabelle löschen";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Autogröße";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Tabellenkopf";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Hinzufügen eines neuen Absatzes";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "kleben";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "kleben";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Text einfügen";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Einfügen als Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Einfügen von Word";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Bitte verwenden Sie STRG+V, um den Inhalt in das Feld unten einzufügen. Der Inhalt wird automatisch gesäubert.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "absätze";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "absätze";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Move Up";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Move Down";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "kopieren";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "streichen";	//"Delete"
RTE_DefaultConfig.text_pmore = "Mehr..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Mehr..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Umschalten der Grenze";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "schnitt";	//"Cut"
RTE_DefaultConfig.text_copy = "kopie";	//"Copy"
RTE_DefaultConfig.text_copied = "kopiert";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Einfügen von Galerie";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Dokument einfügen";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Einfügen von Vorlagen";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "vorschau";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normalen";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "handy";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "tabelle";	//"Table"
RTE_DefaultConfig.text_tablecell = "Tabellenzelle";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Tabellenzeile";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Tabellenspalte";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatik";	//"Automatic"
RTE_DefaultConfig.text_colormore = "mehr";	//"More"
RTE_DefaultConfig.text_colorpicker = "Farbauswahl";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Webpalette";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Benannte Farben";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "basic";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "zusatz";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Drag &amp; Drop";	//"Drag and drop"
RTE_DefaultConfig.text_or = "oder";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Klicken Sie zum Hochladen";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Standard-Bildbeschriftung";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "suche";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Der hinzuzufügende Text hat die Zeichengrenze für dieses Feld erreicht.";	//"The text to be added has reached the character limit for this field."

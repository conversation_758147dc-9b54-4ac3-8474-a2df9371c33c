﻿//Greek , Ελληνικά
RTE_DefaultConfig.text_language = "γλώσσα";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "ακυρώσετε";	//"Cancel"
RTE_DefaultConfig.text_normal = "κανονική";	//"Normal"
RTE_DefaultConfig.text_h1 = "Τίτλος 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Τίτλος 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Τίτλος 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Τίτλος 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Τίτλος 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Τίτλος 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Τίτλος 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "κλείσετε";	//"Close"
RTE_DefaultConfig.text_bold = "τολμηρή";	//"Bold"
RTE_DefaultConfig.text_italic = "πλάγια γραφή";	//"Italic"
RTE_DefaultConfig.text_underline = "υπογράμμιση";	//"Underline"
RTE_DefaultConfig.text_strike = "Γραμμή κρούσης";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "εκθέτη";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Υποκριτές";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Κεφαλαία πεζά";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Πεζά πεζά";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Κατάργηση μορφής";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Εισαγωγή σύνδεσης";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Άνοιγμα σύνδεσης";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Επεξεργασία σύνδεσης";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Κατάργηση σύνδεσης";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Ύψος γραμμής";	//"Line Height"
RTE_DefaultConfig.text_indent = "εσοχή";	//"Indent"
RTE_DefaultConfig.text_outdent = "προεξοχή";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Αποκλεισμός προσφοράς";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Ταξινομημένη λίστα";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Μη ταξινομημένη λίστα";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Εισαγωγή οριζόντιου κανόνα";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Εισαγωγή ημερομηνίας";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Εισαγωγή πίνακα";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Εισαγωγή εικόνας";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Εισαγωγή βίντεο";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Εισαγωγή κώδικα";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Δημιουργία PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Εισαγωγή emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Ειδικοί χαρακτήρες";	//"Special characters"
RTE_DefaultConfig.text_characters = "χαρακτήρες";	//"Characters"
RTE_DefaultConfig.text_fontname = "γραμματοσειρά";	//"Font"
RTE_DefaultConfig.text_fontsize = "μέγεθος";	//"Size"
RTE_DefaultConfig.text_forecolor = "Χρώμα κειμένου";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Χρώμα πλάτης";	//"Back Color"
RTE_DefaultConfig.text_justify = "δικαιολογήσει";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Στοίχιση αριστερά";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Πλήρης στοίχιση δεξιά";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Στοίχιση στο κέντρο";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Πλήρης στοίχιση";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Δεν στοίχιση καμίας";	//"Justify None"
RTE_DefaultConfig.text_delete = "διαγράψετε";	//"Delete"
RTE_DefaultConfig.text_save = "Αποθήκευση αρχείου";	//"Save file"
RTE_DefaultConfig.text_selectall = "Επιλογή όλων";	//"Select All"
RTE_DefaultConfig.text_code = "Κώδικας HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "προεπισκόπηση";	//"Preview"
RTE_DefaultConfig.text_print = "εκτύπωσης";	//"Print"
RTE_DefaultConfig.text_undo = "αναιρέσετε";	//"Undo"
RTE_DefaultConfig.text_redo = "ξανακάνει";	//"Redo"
RTE_DefaultConfig.text_more = "Περισσότερες...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Νέο έγγραφο";	//"New Doc"
RTE_DefaultConfig.text_help = "βοηθήσει";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Προσαρμογή στο παράθυρο";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Έξοδος από την πλήρη οθόνη";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Πρόγραμμα επεξεργασίας εικόνων";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Στυλ εικόνας";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Ενσωματωμένα στυλ";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Στυλ παραγράφων";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Στυλ συνδέσεων";	//"Link Styles"
RTE_DefaultConfig.text_link = "σύνδεση";	//"Link"
RTE_DefaultConfig.text_style = "στυλ";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "διεύθυνση url";	//"Url"
RTE_DefaultConfig.text_byurl = "Κατά διεύθυνση URL";	//"By Url"
RTE_DefaultConfig.text_upload = "ανεβάσετε";	//"Upload"
RTE_DefaultConfig.text_size = "μέγεθος";	//"Size"
RTE_DefaultConfig.text_text = "κείμενο";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Άνοιγμα σε νέα καρτέλα";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "εισαγάγετε";	//"Insert"
RTE_DefaultConfig.text_update = "ενημερωμένη έκδοση";	//"Update"
RTE_DefaultConfig.text_find = "Εύρεση&αντικατάσταση";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "βρείτε";	//"Find"
RTE_DefaultConfig.text_replacewith = "αντικαταστήσει";	//"Replace"
RTE_DefaultConfig.text_findnext = "επόμενη";	//"Next"
RTE_DefaultConfig.text_replaceonce = "αντικαταστήσει";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Αντικατάσταση όλων";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Ταίριασμα πεζών-παι";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Ταίριασμα του Word";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Μετακίνηση προς τα κάτω";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Μετακίνηση προς τα επάνω";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Αυτόματο μέγεθος";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% πλάτος";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "Πλάτος 75%";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "Πλάτος 50%";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% πλάτος";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Ορισμός μεγέθους";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Εναλλακτικό κείμενο";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "δικαιολογήσει";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Λεζάντα εικόνας";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Συγχώνευση κελιών";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Κατακόρυφα διαιρέστακελια";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Οριζόντια διαίρεση κελιών";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Χρώμα κειμένου κελιού";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Χρώμα πίσω κελιού";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Εισαγωγή γραμμής επάνω";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Εισαγωγή γραμμής κάτω";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Εισαγωγή στήλης αριστερά";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Εισαγωγή στήλης δεξιά";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Διαγραφή στήλης";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Διαγραφή γραμμής";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Διαγραφή πίνακα";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Αυτόματο μέγεθος";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Κεφαλίδα πίνακα";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Προσθήκη νέας παραγράφου";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "επικολλήστε";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "επικολλήστε";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Επικόλληση κειμένου";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Επικόλληση ως Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Επικόλληση του Word";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Χρησιμοποιήστε το συνδυασμό πλήκτρων CTRL+V για να επικολλήσετε το περιεχόμενο στο παρακάτω πλαίσιο. \r\nΤο περιεχόμενο θα καθαριστεί αυτόματα.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "παραγράφους";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "παραγράφους";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Μετακίνηση προς τα επάνω";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Μετακίνηση προς τα κάτω";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "διπλότυπες";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "διαγράψετε";	//"Delete"
RTE_DefaultConfig.text_pmore = "Περισσότερες..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Περισσότερες..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Εναλλαγή περιγράμματος";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "κομμένα";	//"Cut"
RTE_DefaultConfig.text_copy = "αντίγραφο";	//"Copy"
RTE_DefaultConfig.text_copied = "αντιγραφεί";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Εισαγωγή συλλογής";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Εισαγωγή εγγράφου";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Εισαγωγή προτύπου";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "προεπισκόπηση";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "κανονική";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "κινητό";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "πίνακα";	//"Table"
RTE_DefaultConfig.text_tablecell = "Κελί πίνακα";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Γραμμή πίνακα";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Στήλη πίνακα";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "αυτόματη";	//"Automatic"
RTE_DefaultConfig.text_colormore = "περισσότερες";	//"More"
RTE_DefaultConfig.text_colorpicker = "Επιλογέας χρώματος";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Παλέτα Web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Επώνυμα χρώματα";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "βασικές";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "επιπλέον";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Μεταφορά και απόθεση";	//"Drag and drop"
RTE_DefaultConfig.text_or = "ή";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Κάντε κλικ για αποστολή";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Προεπιλεγμένη λεζάντα εικόνας";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "αναζήτηση";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Το κείμενο που θα προστεθεί έχει φτάσει στο όριο χαρακτήρων για αυτό το πεδίο.";	//"The text to be added has reached the character limit for this field."

﻿//Spanish , Español
RTE_DefaultConfig.text_language = "idioma";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "cancelar";	//"Cancel"
RTE_DefaultConfig.text_normal = "normal";	//"Normal"
RTE_DefaultConfig.text_h1 = "Título 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Título 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Título 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Título 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Título 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Título 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Título 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "cerca";	//"Close"
RTE_DefaultConfig.text_bold = "atrevido";	//"Bold"
RTE_DefaultConfig.text_italic = "cursiva";	//"Italic"
RTE_DefaultConfig.text_underline = "subrayar";	//"Underline"
RTE_DefaultConfig.text_strike = "Línea de huelga";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "superíndice";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Caso superior";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Minúsculas";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Eliminar formato";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Insertar enlace";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Abrir enlace";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Editar enlace";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Eliminar enlace";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Altura de la línea";	//"Line Height"
RTE_DefaultConfig.text_indent = "sangrar";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Cotización de bloque";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Lista ordenada";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Lista desordenada";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Insertar regla horizontal";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Insertar fecha";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Insertar tabla";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Insertar imagen";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Insertar vídeo";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Insertar código";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Crear PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Insertar Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Personajes especiales";	//"Special characters"
RTE_DefaultConfig.text_characters = "caracteres";	//"Characters"
RTE_DefaultConfig.text_fontname = "fuente";	//"Font"
RTE_DefaultConfig.text_fontsize = "color";	//"Size"
RTE_DefaultConfig.text_forecolor = "Color del texto";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Color de la espalda";	//"Back Color"
RTE_DefaultConfig.text_justify = "justificar";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Justificar izquierda";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Justificar la derecha";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Centro de Justificación";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Justificar completo";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Justificar ninguno";	//"Justify None"
RTE_DefaultConfig.text_delete = "eliminar";	//"Delete"
RTE_DefaultConfig.text_save = "Guardar archivo";	//"Save file"
RTE_DefaultConfig.text_selectall = "Seleccionar todo";	//"Select All"
RTE_DefaultConfig.text_code = "Código HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "vista previa";	//"Preview"
RTE_DefaultConfig.text_print = "imprimir";	//"Print"
RTE_DefaultConfig.text_undo = "deshacer";	//"Undo"
RTE_DefaultConfig.text_redo = "rehacer";	//"Redo"
RTE_DefaultConfig.text_more = "Más...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Nuevo documento";	//"New Doc"
RTE_DefaultConfig.text_help = "ayudar";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Ajuste a la ventana";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Salir de la pantalla completa";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Editor de imágenes";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Estilos de imagen";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Estilos en línea";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Estilos de párrafo";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Estilos de enlace";	//"Link Styles"
RTE_DefaultConfig.text_link = "enlace";	//"Link"
RTE_DefaultConfig.text_style = "estilos";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Clases Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Por Url";	//"By Url"
RTE_DefaultConfig.text_upload = "subir";	//"Upload"
RTE_DefaultConfig.text_size = "color";	//"Size"
RTE_DefaultConfig.text_text = "texto";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Abrir en una nueva pestaña";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "insertar";	//"Insert"
RTE_DefaultConfig.text_update = "actualizar";	//"Update"
RTE_DefaultConfig.text_find = "Buscar&a reemplazar";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "encontrar";	//"Find"
RTE_DefaultConfig.text_replacewith = "reemplazar";	//"Replace"
RTE_DefaultConfig.text_findnext = "siguiente";	//"Next"
RTE_DefaultConfig.text_replaceonce = "reemplazar";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Reemplazar todo";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Caso de partido";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Match Word";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Mover hacia abajo";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Move Up";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Tamaño automático";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% de ancho";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% de ancho";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% de ancho";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% de ancho";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Establecer tamaño";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Texto alternativo";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "justificar";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Image Caption";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Fusionar celdas";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Células divididas Verticales";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Células divididas Horizontal";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Color del texto de la celda";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Color de la espalda de la celda";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Insertar fila arriba";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Insertar fila debajo";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Insertar columna a la izquierda";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Insertar columna a la derecha";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Eliminar columna";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Eliminar fila";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Eliminar tabla";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Tamaño automático";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Encabezado de tabla";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Añadir un nuevo párrafo";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "pegar";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "pegar";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Pegar texto";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Pegar como Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Pegar palabra";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Utilice CTRL+V para pegar el contenido en el cuadro siguiente. El contenido se limpiará automáticamente.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "párrafos";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "párrafos";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Move Up";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Mover hacia abajo";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "duplicado";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "eliminar";	//"Delete"
RTE_DefaultConfig.text_pmore = "Más..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Más..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Alternar borde";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "corte";	//"Cut"
RTE_DefaultConfig.text_copy = "copia";	//"Copy"
RTE_DefaultConfig.text_copied = "copiado";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Insertar galería";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Insertar documento";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Insertar plantilla";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "vista previa";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normal";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "móvil";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tableta";	//"Tablet"
RTE_DefaultConfig.text_table = "tabla";	//"Table"
RTE_DefaultConfig.text_tablecell = "Celda de mesa";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Fila de la tabla";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Columna de la tabla";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automático";	//"Automatic"
RTE_DefaultConfig.text_colormore = "más";	//"More"
RTE_DefaultConfig.text_colorpicker = "Selector de color";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Paleta Web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Colores nombrados";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "base";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "adición";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Arrastrar y soltar";	//"Drag and drop"
RTE_DefaultConfig.text_or = "qué";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Haga clic para cargar";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Subtítulo de imagen predeterminado";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "buscar";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "El texto que se va a agregar ha alcanzado el límite de caracteres para este campo.";	//"The text to be added has reached the character limit for this field."

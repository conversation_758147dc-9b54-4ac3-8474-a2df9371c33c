﻿//Persian , Persian
RTE_DefaultConfig.text_language = "زبان";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "لغو";	//"Cancel"
RTE_DefaultConfig.text_normal = "عادی";	//"Normal"
RTE_DefaultConfig.text_h1 = "تیتر 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "تیتر 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "تیتر 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "تیتر 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "تیتر 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "تیتر 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "تیتر 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "بستن";	//"Close"
RTE_DefaultConfig.text_bold = "جسورانه";	//"Bold"
RTE_DefaultConfig.text_italic = "مایل";	//"Italic"
RTE_DefaultConfig.text_underline = "زیر خط";	//"Underline"
RTE_DefaultConfig.text_strike = "اعتصاب خط";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "پرانتز";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "حروف بزرگ";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "حروف کوچک";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "حذف شکلبندی";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "درج پیوند";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "باز کردن پیوند";	//"Open Link"
RTE_DefaultConfig.text_editlink = "ویرایش پیوند";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "حذف پیوند";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "ارتفاع خط";	//"Line Height"
RTE_DefaultConfig.text_indent = "تورفتگی";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "بلوک نقل قول";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "لیست سفارش";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "لیست غیر مرتب";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "درج قانون افقی";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "تاریخ را وارد کنید";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "درج جدول";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "درج تصویر";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "درج ویدیو";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "درج کد";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "ایجاد PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "درج شکلک";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "کاراکترهای خاص";	//"Special characters"
RTE_DefaultConfig.text_characters = "کاراکتر";	//"Characters"
RTE_DefaultConfig.text_fontname = "فونت";	//"Font"
RTE_DefaultConfig.text_fontsize = "اندازه";	//"Size"
RTE_DefaultConfig.text_forecolor = "رنگ متن";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "رنگ پشت";	//"Back Color"
RTE_DefaultConfig.text_justify = "توجیه";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "همتراز چپ";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "همتراز راست";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "تراز مرکز";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "همتراز کامل";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "توجیه هیچ";	//"Justify None"
RTE_DefaultConfig.text_delete = "حذف";	//"Delete"
RTE_DefaultConfig.text_save = "ذخیره پرونده";	//"Save file"
RTE_DefaultConfig.text_selectall = "انتخاب همه";	//"Select All"
RTE_DefaultConfig.text_code = "کد اچ تی ام";	//"HTML Code"
RTE_DefaultConfig.text_preview = "پیشنمایش";	//"Preview"
RTE_DefaultConfig.text_print = "چاپ";	//"Print"
RTE_DefaultConfig.text_undo = "خنثیسازی";	//"Undo"
RTE_DefaultConfig.text_redo = "ازنو";	//"Redo"
RTE_DefaultConfig.text_more = "بیشتر...";	//"More..."
RTE_DefaultConfig.text_newdoc = "جدید فیلم کارگردان تهیه کننده";	//"New Doc"
RTE_DefaultConfig.text_help = "کمک";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "گنجاندن در پنجره";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "خروج از تمام صفحه";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "ویرایشگر تصویر";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "سبک های تصویر";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "سبکهای درون خطی";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "سبکهای پاراگراف";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "سبکهای پیوند";	//"Link Styles"
RTE_DefaultConfig.text_link = "لینک";	//"Link"
RTE_DefaultConfig.text_style = "سبک";	//"Styles"
RTE_DefaultConfig.text_cssclass = "کلاس های Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "آدرس";	//"Url"
RTE_DefaultConfig.text_byurl = "توسط Url";	//"By Url"
RTE_DefaultConfig.text_upload = "آپلود";	//"Upload"
RTE_DefaultConfig.text_size = "اندازه";	//"Size"
RTE_DefaultConfig.text_text = "متن";	//"Text"
RTE_DefaultConfig.text_opennewwin = "گشودن در زبانه جدید";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "درج";	//"Insert"
RTE_DefaultConfig.text_update = "روز رسانی";	//"Update"
RTE_DefaultConfig.text_find = "یافتن & Replace";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "پیدا کردن";	//"Find"
RTE_DefaultConfig.text_replacewith = "جایگزین";	//"Replace"
RTE_DefaultConfig.text_findnext = "بعدی";	//"Next"
RTE_DefaultConfig.text_replaceonce = "جایگزین";	//"Replace"
RTE_DefaultConfig.text_replaceall = "جایگزین همه";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "تطبیق مورد";	//"Match Case"
RTE_DefaultConfig.text_matchword = "بازی کلمه";	//"Match Word"
RTE_DefaultConfig.text_move_down = "حرکت به پایین";	//"Move Down"
RTE_DefaultConfig.text_move_up = "حرکت به بالا";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "اندازه خودکار";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "۱۰۰% عرض";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "۷۵% عرض";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "۵۰% عرض";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% عرض";	//"25% width"
RTE_DefaultConfig.text_controlsize = "تنظیم اندازه";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "متن Alt";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "توجیه";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "عنوان تصویر";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "ادغام سلولها";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "سلولهای تقسیم شده عمودی";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "سلولهای تقسیم شده افقی";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "رنگ متن سلول";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "رنگ پشت سلول";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "درج ردیف بالا";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "درج ردیف زیر";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "درج ستون به چپ";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "درج راست ستون";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "حذف ستون";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "حذف ردیف";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "حذف جدول";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "اندازه خودکار";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "سربرگ جدول";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "افزودن پاراگراف جدید";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "جای گذاری";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "جای گذاری";	//"Paste"
RTE_DefaultConfig.text_pastetext = "جای گذاری متن";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "جای گذاری بعنوان Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "جای گذاری کلمه";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "لطفا از CTRL + V برای چسباندن محتوا در جعبه زیر استفاده کنید. محتوای \r\nThe به طور خودکار تمیز می شود.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "پاراگراف";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "پاراگراف";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "حرکت به بالا";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "حرکت به پایین";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "تکراری";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "حذف";	//"Delete"
RTE_DefaultConfig.text_pmore = "بیشتر..";	//"More.."
RTE_DefaultConfig.text_togglemore = "بیشتر..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "تعویض مرز";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "برش";	//"Cut"
RTE_DefaultConfig.text_copy = "کپی";	//"Copy"
RTE_DefaultConfig.text_copied = "کپی";	//"copied"
RTE_DefaultConfig.text_insertgallery = "درج گالری";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "درج نوشتار";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "درج الگو";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "پیشنمایش";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "عادی";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "موبایل";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "قرص";	//"Tablet"
RTE_DefaultConfig.text_table = "جدول";	//"Table"
RTE_DefaultConfig.text_tablecell = "سلول جدول";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "ردیف جدول";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "ستون جدول";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "خودکار";	//"Automatic"
RTE_DefaultConfig.text_colormore = "بیشتر";	//"More"
RTE_DefaultConfig.text_colorpicker = "جمع کننده رنگ";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "پالت وب";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "رنگ های نامگذاری";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "اساسی";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "علاوه";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "کشیدن و رها کردن";	//"Drag and drop"
RTE_DefaultConfig.text_or = "یا";	//"or"
RTE_DefaultConfig.text_clicktoupload = "برای آپلود کلیک کنید";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "زیرنویس تصویر پیش فرض";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "جستجو";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "متن افزوده شده به حد نویسه برای این زمینه رسیده است.";	//"The text to be added has reached the character limit for this field."

﻿//French , Français
RTE_DefaultConfig.text_language = "langue";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "annuler";	//"Cancel"
RTE_DefaultConfig.text_normal = "habituel";	//"Normal"
RTE_DefaultConfig.text_h1 = "Headline 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Headline 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Headline 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Headline 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Headline 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Headline 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Headline 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "fermer";	//"Close"
RTE_DefaultConfig.text_bold = "gras";	//"Bold"
RTE_DefaultConfig.text_italic = "italique";	//"Italic"
RTE_DefaultConfig.text_underline = "souligner";	//"Underline"
RTE_DefaultConfig.text_strike = "Ligne de grève";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "superscript";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Sous-puppt";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Cas supérieur";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Cas inférieur";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Supprimer le format";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Insérer le lien";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Lien ouvert";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Modifier le lien";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Supprimer le lien";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Hauteur de la ligne";	//"Line Height"
RTE_DefaultConfig.text_indent = "tiret";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Citation de bloc";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Liste commandée";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Liste non ordonnée";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Insérer une règle horizontale";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Date d’insertion";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Insérer la table";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Insérer l’image";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Insérer la vidéo";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Code d’insertion";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Créer PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Insérer Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Caractères spéciaux";	//"Special characters"
RTE_DefaultConfig.text_characters = "personnages";	//"Characters"
RTE_DefaultConfig.text_fontname = "police";	//"Font"
RTE_DefaultConfig.text_fontsize = "taille";	//"Size"
RTE_DefaultConfig.text_forecolor = "Couleur de texte";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Couleur arrière";	//"Back Color"
RTE_DefaultConfig.text_justify = "justifier";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Justifier la gauche";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Justifier le droit";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Centre de justification";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Justifier full";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Justify None";	//"Justify None"
RTE_DefaultConfig.text_delete = "supprimer";	//"Delete"
RTE_DefaultConfig.text_save = "Enregistrer le fichier";	//"Save file"
RTE_DefaultConfig.text_selectall = "Sélectionnez tous les";	//"Select All"
RTE_DefaultConfig.text_code = "HTML Code";	//"HTML Code"
RTE_DefaultConfig.text_preview = "prévisualisation";	//"Preview"
RTE_DefaultConfig.text_print = "imprimer";	//"Print"
RTE_DefaultConfig.text_undo = "annuler";	//"Undo"
RTE_DefaultConfig.text_redo = "refaire";	//"Redo"
RTE_DefaultConfig.text_more = "Plus...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Nouveau Doc";	//"New Doc"
RTE_DefaultConfig.text_help = "aider";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "S’adapter à la fenêtre";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Sortir plein écran";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Éditeur d’images";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Styles d’image";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Inline Styles";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Styles de paragraphes";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Styles de liens";	//"Link Styles"
RTE_DefaultConfig.text_link = "lien";	//"Link"
RTE_DefaultConfig.text_style = "styles";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css Classes";	//"Css Classes"
RTE_DefaultConfig.text_url = "adresse";	//"Url"
RTE_DefaultConfig.text_byurl = "Par Url";	//"By Url"
RTE_DefaultConfig.text_upload = "télécharger";	//"Upload"
RTE_DefaultConfig.text_size = "taille";	//"Size"
RTE_DefaultConfig.text_text = "texte";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Ouvrir dans un nouvel onglet";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "insérer";	//"Insert"
RTE_DefaultConfig.text_update = "mise";	//"Update"
RTE_DefaultConfig.text_find = "Trouver-Remplacer";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "trouver";	//"Find"
RTE_DefaultConfig.text_replacewith = "remplacer";	//"Replace"
RTE_DefaultConfig.text_findnext = "prochain";	//"Next"
RTE_DefaultConfig.text_replaceonce = "remplacer";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Remplacer tous les";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Cas de match";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Mot de match";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Déplacer vers le bas";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Déplacer vers le haut";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Taille automatique";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% de largeur";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% de largeur";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% de largeur";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% de largeur";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Taille de l’ensemble";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Texte Alt";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "justifier";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Légende de l’image";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Cellules de fusion";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Cellules divisées Verticale";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Cellules fractionnées horizontales";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Couleur de texte de cellules";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Couleur de dos de cellules";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Insérer la ligne ci-dessus";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Insérer la ligne ci-dessous";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Insérer la colonne gauche";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Insérer la colonne droite";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Supprimer la colonne";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Supprimer la ligne";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Supprimer la table";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Taille automatique";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "En-tête de table";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Ajouter un nouveau paragraphe";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "coller";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "coller";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Texte de pâte";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Pâte comme Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Mot pâte";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Veuillez utiliser CTRL+V pour coller le contenu dans la zone ci-dessous. \r\nLe contenu sera nettoyé automatiquement.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "paragraphes";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "paragraphes";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Déplacer vers le haut";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Déplacer vers le bas";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "double";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "supprimer";	//"Delete"
RTE_DefaultConfig.text_pmore = "Plus..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Plus..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Bordure de bascule";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "couper";	//"Cut"
RTE_DefaultConfig.text_copy = "copie";	//"Copy"
RTE_DefaultConfig.text_copied = "copié";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Galerie d’insertion";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Insérer un document";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Insérer un modèle";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "prévisualisation";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "habituel";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "portable";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tablette";	//"Tablet"
RTE_DefaultConfig.text_table = "table";	//"Table"
RTE_DefaultConfig.text_tablecell = "Cellule de table";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Ligne de table";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Colonne de table";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatique";	//"Automatic"
RTE_DefaultConfig.text_colormore = "plus";	//"More"
RTE_DefaultConfig.text_colorpicker = "Sélecteur de couleurs";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Web Palette";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Couleurs nommées";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "base";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "ajout";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Glisser et déposer";	//"Drag and drop"
RTE_DefaultConfig.text_or = "ou";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Cliquez pour télécharger";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Légende de l’image par défaut";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "rechercher";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Le texte à ajouter a atteint la limite de caractères pour ce champ.";	//"The text to be added has reached the character limit for this field."

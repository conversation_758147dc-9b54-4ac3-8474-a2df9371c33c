﻿//Haitian Creole , Haitian Creole
RTE_DefaultConfig.text_language = "lang";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "anile";	//"Cancel"
RTE_DefaultConfig.text_normal = "nòmal";	//"Normal"
RTE_DefaultConfig.text_h1 = "Tit 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Tit 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Tit 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Tit 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Tit 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Tit 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Tit 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "fèmen";	//"Close"
RTE_DefaultConfig.text_bold = "Fonse";	//"Bold"
RTE_DefaultConfig.text_italic = "Italik";	//"Italic"
RTE_DefaultConfig.text_underline = "Souliye";	//"Underline"
RTE_DefaultConfig.text_strike = "Grev liy";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "Superscript";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Upper ka";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Pi ba ka";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Retire Foma";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Mete Link";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Louvri Link";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Edit Link";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Retire Link";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Liy wote";	//"Line Height"
RTE_DefaultConfig.text_indent = "Endis";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Blok quote";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Lis lod";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Lis ki bay lod";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Mete orizontal reg";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Mete dat";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Mete tab";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Mete imaj";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Mete videyo";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Mete Kod";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Kreye PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Mete Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Karakte espesyal";	//"Special characters"
RTE_DefaultConfig.text_characters = "Karakte";	//"Characters"
RTE_DefaultConfig.text_fontname = "Font";	//"Font"
RTE_DefaultConfig.text_fontsize = "grandè";	//"Size"
RTE_DefaultConfig.text_forecolor = "Koule teks";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Retounen koule";	//"Back Color"
RTE_DefaultConfig.text_justify = "Jistifye";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Jistifye Left";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Jistifye dwa";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Jistifye sant";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Jistifye plen";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Jistifye okenn";	//"Justify None"
RTE_DefaultConfig.text_delete = "Efase";	//"Delete"
RTE_DefaultConfig.text_save = "Sove dosye";	//"Save file"
RTE_DefaultConfig.text_selectall = "Chwazi tout";	//"Select All"
RTE_DefaultConfig.text_code = "HTML Kod";	//"HTML Code"
RTE_DefaultConfig.text_preview = "Preview";	//"Preview"
RTE_DefaultConfig.text_print = "enprime";	//"Print"
RTE_DefaultConfig.text_undo = "Defet";	//"Undo"
RTE_DefaultConfig.text_redo = "Refe";	//"Redo"
RTE_DefaultConfig.text_more = "Plis...";	//"More..."
RTE_DefaultConfig.text_newdoc = "New Doc";	//"New Doc"
RTE_DefaultConfig.text_help = "ede";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Anfom nan Fenet";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Soti ekran plen";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Edite imaj";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Styles imaj";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Inline estil";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Paragraf Styles";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Link estil";	//"Link Styles"
RTE_DefaultConfig.text_link = "lyen";	//"Link"
RTE_DefaultConfig.text_style = "Estil";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Klas Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "Url";	//"Url"
RTE_DefaultConfig.text_byurl = "Pa Url";	//"By Url"
RTE_DefaultConfig.text_upload = "Upload";	//"Upload"
RTE_DefaultConfig.text_size = "grandè";	//"Size"
RTE_DefaultConfig.text_text = "Teks";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Louvri nan nouvo tab";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "Mete";	//"Insert"
RTE_DefaultConfig.text_update = "Mete ajou";	//"Update"
RTE_DefaultConfig.text_find = "Jwenn & ranplase";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "jwenn";	//"Find"
RTE_DefaultConfig.text_replacewith = "ranplase";	//"Replace"
RTE_DefaultConfig.text_findnext = "pwochen";	//"Next"
RTE_DefaultConfig.text_replaceonce = "ranplase";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Ranplase tout";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Koresponn ak ka";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Koresponn ak pawol";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Deplase desann";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Deplase moute";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Gwose Auto";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% laje";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% laje";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% laje";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% laje";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Mete gwose";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt teks";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "Jistifye";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Imaj Caption";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Konble selil";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Selil Split Vetikal";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Selil Split orizontal";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Selil teks koule";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Selil retounen koule";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Mete ranje pi wo a";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Mete ranje anba a";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Mete kolon Left";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Mete kolon dwa";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Efase kolon";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Efase ranje";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Efase tablo";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Gwose Auto";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Table Header";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Ajoute yon paragraf nouvo";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "Kole";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "Kole";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Teks Paste";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Kole kom html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Pawol kole";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Tanpri itilize CTRL + V kole kontni an nan bwat anba a. \r\nThe kontni pral netwaye otomatikman.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "Paragraf";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "Paragraf";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Deplase moute";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Deplase desann";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "Kopi";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "Efase";	//"Delete"
RTE_DefaultConfig.text_pmore = "Plis..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Plis..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Baskile Border";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "Koupé";	//"Cut"
RTE_DefaultConfig.text_copy = "kopi";	//"Copy"
RTE_DefaultConfig.text_copied = "kopye";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Mete galeri";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Mete dokiman";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Mete Model";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "Preview";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "nòmal";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "Mobil";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "konprime";	//"Tablet"
RTE_DefaultConfig.text_table = "tab";	//"Table"
RTE_DefaultConfig.text_tablecell = "Telefon tablo";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Tablo Ranje";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Tablo Kolon";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "otomatik";	//"Automatic"
RTE_DefaultConfig.text_colormore = "plis";	//"More"
RTE_DefaultConfig.text_colorpicker = "Koule Piker";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Web Palet";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Yo rele koule";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "fondamantal";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "adisyon";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Trennen ak gout";	//"Drag and drop"
RTE_DefaultConfig.text_or = "oubyen";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Klike sou yo upload";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Default imaj Caption";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "fouye";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Te teks la dwe ajoute te rive nan limit la karakte pou jaden sa a.";	//"The text to be added has reached the character limit for this field."

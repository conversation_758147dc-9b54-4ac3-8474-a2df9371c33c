﻿//Malayalam , മലയാളം
RTE_DefaultConfig.text_language = "ഭാഷ";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "ക്യാൻസൽ ചെയ്യ്";	//"Cancel"
RTE_DefaultConfig.text_normal = "സാധാരണ";	//"Normal"
RTE_DefaultConfig.text_h1 = "തലക്കെട്ട് 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "തലക്കെട്ട് 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "തലക്കെട്ട് 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "തലക്കെട്ട് 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "തലക്കെട്ട് 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "തലക്കെട്ട് 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "തലക്കെട്ട് 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "അടുത്ത";	//"Close"
RTE_DefaultConfig.text_bold = "ധീരമായ";	//"Bold"
RTE_DefaultConfig.text_italic = "ചരിഞ്ഞ";	//"Italic"
RTE_DefaultConfig.text_underline = "അടിവരയിട്ട്";	//"Underline"
RTE_DefaultConfig.text_strike = "സ്ട്രൈക്ക് ലൈൻ";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "സൂപ്പർസ്ക്രിപ്റ്റ്";	//"Superscript"
RTE_DefaultConfig.text_subscript = "സബ്സ്ക്രിപ്റ്റ്";	//"Subcript"
RTE_DefaultConfig.text_ucase = "അപ്പര് കേസ്";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "കീഴ് കേസ്";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "രീതി നീക്കം ചെയ്യുക";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "കണ്ണി ചേര് ക്കുക";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "തുറന്ന കണ്ണി";	//"Open Link"
RTE_DefaultConfig.text_editlink = "കണ്ണി ചിട്ടപ്പെടുത്തുക";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "കണ്ണി നീക്കം ചെയ്യുക";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "വരി ഉയരം";	//"Line Height"
RTE_DefaultConfig.text_indent = "ഓഫര്";	//"Indent"
RTE_DefaultConfig.text_outdent = "ഔട്ട്ഡന്റ്";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "ബ്ലോക്ക് ഉദ്ധരണി";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "ഓർഡർ ലിസ്റ്റ്";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "ഓർഡർ ചെയ്യാത്ത ലിസ്റ്റ്";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "തിരശ്ചീന നിയമം ചേർക്കുക";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "തീയതി ചേര് ക്കുക";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "പട്ടിക തിരുകുക";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "ചിത്രം തിരുകുക";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "വീഡിയോ ചേര് ക്കുക";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "കോഡ് ചേര് ക്കുക";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "സൃഷ്ടിക്കുക PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "എജബിജി ചേര് ക്കുക";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "പ്രത്യേക പ്രതീകങ്ങൾ";	//"Special characters"
RTE_DefaultConfig.text_characters = "കഥാപാത്രങ്ങളെ";	//"Characters"
RTE_DefaultConfig.text_fontname = "ഫോണ്ട്";	//"Font"
RTE_DefaultConfig.text_fontsize = "വലിപ്പം";	//"Size"
RTE_DefaultConfig.text_forecolor = "പദാവലിയുടെ നിറം";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "ബാക്ക് കളർ";	//"Back Color"
RTE_DefaultConfig.text_justify = "ന്യായീകരിക്കാന്";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "ഇടതു് ന്യായീകരിക്കുക";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "ശരിയെ ന്യായീകരിക്കുക";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "സെന്റർ ന്യായീകരിക്കുക";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "പൂര് ണ്ണതയെ ന്യായീകരിക്കുക";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "ഒന്നുമില്ലെനെ ന്യായീകരിക്കുക";	//"Justify None"
RTE_DefaultConfig.text_delete = "ഇല്ലാതാക്കാന്";	//"Delete"
RTE_DefaultConfig.text_save = "ഫയല് സൂക്ഷിക്കുക";	//"Save file"
RTE_DefaultConfig.text_selectall = "എല്ലാം തെരഞ്ഞെടുക്കുക";	//"Select All"
RTE_DefaultConfig.text_code = "HTML കോഡ്";	//"HTML Code"
RTE_DefaultConfig.text_preview = "പ്രിവ്യൂവിന്റെ";	//"Preview"
RTE_DefaultConfig.text_print = "അച്ചടിക്കുക";	//"Print"
RTE_DefaultConfig.text_undo = "പഴയപടിയാക്കാൻ";	//"Undo"
RTE_DefaultConfig.text_redo = "Redo";	//"Redo"
RTE_DefaultConfig.text_more = "കൂടുതൽ...";	//"More..."
RTE_DefaultConfig.text_newdoc = "പുതിയ ഡോക്";	//"New Doc"
RTE_DefaultConfig.text_help = "സഹായം";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "ജാലകത്തിലേക്ക് ഫിറ്റ് ചെയ്യുക";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "മുഴുവന് സ്ക്രീനില് നിന്നും പുറത്തുകടക്കുക";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "ഇമേജ് എഡിറ്റര്";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "ചിത്രശൈലികള്";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "ഇൻലൈൻ ശൈലികൾ";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "ഖണ്ഡിക യുടെ ശൈലികൾ";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "ലിങ്ക് ശൈലികൾ";	//"Link Styles"
RTE_DefaultConfig.text_link = "ലിങ്ക്";	//"Link"
RTE_DefaultConfig.text_style = "ശൈലികൾ";	//"Styles"
RTE_DefaultConfig.text_cssclass = "സിഎസ്എസ് ക്ലാസുകൾ";	//"Css Classes"
RTE_DefaultConfig.text_url = "Url";	//"Url"
RTE_DefaultConfig.text_byurl = "യുആര് എല്";	//"By Url"
RTE_DefaultConfig.text_upload = "അപ് ലോഡ്";	//"Upload"
RTE_DefaultConfig.text_size = "വലിപ്പം";	//"Size"
RTE_DefaultConfig.text_text = "ടെക്സ്റ്റ്";	//"Text"
RTE_DefaultConfig.text_opennewwin = "പുതിയ കിളിവാതിലില് തുറക്കുക";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "തിരുകുക";	//"Insert"
RTE_DefaultConfig.text_update = "നവീകരണ";	//"Update"
RTE_DefaultConfig.text_find = "& മാറ്റുക";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "കണ്ടെത്താന്";	//"Find"
RTE_DefaultConfig.text_replacewith = "മാറ്റി";	//"Replace"
RTE_DefaultConfig.text_findnext = "അടുത്ത";	//"Next"
RTE_DefaultConfig.text_replaceonce = "മാറ്റി";	//"Replace"
RTE_DefaultConfig.text_replaceall = "എല്ലാം മാറ്റുക";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "മത്സര കേസില്";	//"Match Case"
RTE_DefaultConfig.text_matchword = "മത്സര വാക്ക്";	//"Match Word"
RTE_DefaultConfig.text_move_down = "താഴേക്ക് നീക്കുക";	//"Move Down"
RTE_DefaultConfig.text_move_up = "മുകളിലേക്ക് നീക്കുക";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "യാന്ത്രിക വലിപ്പം";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% വീതി";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% വീതി";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% വീതി";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% വീതി";	//"25% width"
RTE_DefaultConfig.text_controlsize = "വലിപ്പം ക്രമീകരിക്കുക";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "ആൾട്ട് ടെക്സ്റ്റ്";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "ന്യായീകരിക്കാന്";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "ചിത്രത്തിന്റെ അടിക്കുറിപ്പ്";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "കോശങ്ങളെ ലയിപ്പിക്കുക";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "കോശങ്ങളുടെ പിളര് പ്പ്";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "കോശങ്ങളുടെ തിരശ്ചീനമായി പിളരാനുള്ള";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "കോശ പദാവലിയുടെ നിറം";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "സെല് ബാക്ക് നിറം";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "മുകളില് വരി ചേര് ക്കുക";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "താഴെ വരി ചേര് ക്കുക";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "കോളം ഇടയ്ക്കു് ചേര് ക്കുക";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "കോളം ശരിയായി ചേര് ക്കുക";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "നിര മായ്ക്കുക";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "വരി വെട്ടി മാറ്റുക";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "പട്ടിക നീക്കം ചെയ്യുക";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "യാന്ത്രിക വലിപ്പം";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "ടേബിള് ഹെഡര്";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "ഒരു പുതിയ ഖണ്ഡിക ചേർക്കുക";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "പേസ്റ്റ്";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "പേസ്റ്റ്";	//"Paste"
RTE_DefaultConfig.text_pastetext = "പദാവലി ഒട്ടിയ്ക്കുക";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Html ആയി പേസ്റ്റ് ചെയ്യുക";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "& ഒട്ടിയ്ക്കുക";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "താഴെയുള്ള ബോക്സിൽ ഉള്ളടക്കം ഒട്ടിക്കാൻ CTRL+V ഉപയോഗിക്കുക. \r\nഉള്ളടക്കം സ്വയമേവ വൃത്തിയാക്കപ്പെടും.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "ഖണ്ഡികകൾ";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "ഖണ്ഡികകൾ";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "മുകളിലേക്ക് നീക്കുക";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "താഴേക്ക് നീക്കുക";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "à ́à μà";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "ഇല്ലാതാക്കാന്";	//"Delete"
RTE_DefaultConfig.text_pmore = "..";	//"More.."
RTE_DefaultConfig.text_togglemore = "..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "അതിര് ത്തി ടോഗിള് ചെയ്യുക";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "മുറിക്കുക";	//"Cut"
RTE_DefaultConfig.text_copy = "പകര് ത്തുക";	//"Copy"
RTE_DefaultConfig.text_copied = "പകര് ത്തിയ";	//"copied"
RTE_DefaultConfig.text_insertgallery = "ഗാലറി ചേർക്കുക";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "രേഖ ചേര് ക്കുക";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "ഫലകം ചേർക്കുക";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "പ്രിവ്യൂവിന്റെ";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "സാധാരണ";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "മൊബൈൽ";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "ടാബ് ലെറ്റ്";	//"Tablet"
RTE_DefaultConfig.text_table = "പട്ടിക";	//"Table"
RTE_DefaultConfig.text_tablecell = "ടേബിൾ സെൽ";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "ടേബിൾ വരി";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "പട്ടികനിര";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "തനിയെ";	//"Automatic"
RTE_DefaultConfig.text_colormore = "കൂടുതൽ";	//"More"
RTE_DefaultConfig.text_colorpicker = "നിറം പിക്കർ";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "വെബ് പാലെറ്റ്";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "പേരിട്ട നിറങ്ങൾ";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "അടിസ്ഥാനം";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "കൂട്ടിച്ചേർക്കൽ";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "വലിച്ചുനീക്കുക, ഡ്രോപ്പ് ചെയ്യുക";	//"Drag and drop"
RTE_DefaultConfig.text_or = "അല്ലെങ്കിൽ";	//"or"
RTE_DefaultConfig.text_clicktoupload = "അപ് ലോഡ് ചെയ്യാൻ ക്ലിക്കുചെയ്യുക";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "സഹജമായ ചിത്രഅടിക്കുറിപ്പ്";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "തെരയുക";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "ഈ ഫീൽഡിന്റെ അക്ഷരപരിധിയിലേക്ക് ചേർത്തിരിക്കുന്ന പദാവലി.";	//"The text to be added has reached the character limit for this field."

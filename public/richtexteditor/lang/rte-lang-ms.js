﻿//Malay , Melayu
RTE_DefaultConfig.text_language = "bahasa";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "membatalkan";	//"Cancel"
RTE_DefaultConfig.text_normal = "biasa";	//"Normal"
RTE_DefaultConfig.text_h1 = "Tajuk 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Tajuk 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Tajuk 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Tajuk 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Tajuk 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Tajuk 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Tajuk 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "tutup";	//"Close"
RTE_DefaultConfig.text_bold = "berani";	//"Bold"
RTE_DefaultConfig.text_italic = "Italik";	//"Italic"
RTE_DefaultConfig.text_underline = "gariskan";	//"Underline"
RTE_DefaultConfig.text_strike = "Talian Strike";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "Superskrip";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Kes Upper";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Huruf kecil";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Alih keluar format";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Masukkan pautan";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Buka pautan";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Edit pautan";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Alih Keluar pautan";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Ketinggian garis";	//"Line Height"
RTE_DefaultConfig.text_indent = "Inden";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outkemam";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Sekat petikan";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Senarai disusun";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Senarai tidak disusun";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Sisipkan Peraturan melintang";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Masukkan tarikh";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Masukkan Jadual";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Masukkan imej";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Masukkan video";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Masukkan kod";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Buat PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Masukkan Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Aksara khas";	//"Special characters"
RTE_DefaultConfig.text_characters = "aksara";	//"Characters"
RTE_DefaultConfig.text_fontname = "fon";	//"Font"
RTE_DefaultConfig.text_fontsize = "saiz";	//"Size"
RTE_DefaultConfig.text_forecolor = "Warna teks";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Warna belakang";	//"Back Color"
RTE_DefaultConfig.text_justify = "mewajarkan";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Justifikasi kiri";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Mewajarkan hak";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Pusat justifikasi";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Mewajarkan penuh";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Mewajarkan tiada";	//"Justify None"
RTE_DefaultConfig.text_delete = "menghapuskan";	//"Delete"
RTE_DefaultConfig.text_save = "Simpan fail";	//"Save file"
RTE_DefaultConfig.text_selectall = "Pilih semua";	//"Select All"
RTE_DefaultConfig.text_code = "Kod HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "pratonton";	//"Preview"
RTE_DefaultConfig.text_print = "cetakan";	//"Print"
RTE_DefaultConfig.text_undo = "membatalkan";	//"Undo"
RTE_DefaultConfig.text_redo = "Buat semula";	//"Redo"
RTE_DefaultConfig.text_more = "Lebih banyak...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Doc baru";	//"New Doc"
RTE_DefaultConfig.text_help = "membantu";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Muatkan pada tetingkap";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Keluar skrin penuh";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Penyunting imej";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Gaya imej";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Gaya sebaris";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Gaya perenggan";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Gaya pautan";	//"Link Styles"
RTE_DefaultConfig.text_link = "pautan";	//"Link"
RTE_DefaultConfig.text_style = "gaya";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Kelas CSS";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Oleh URL";	//"By Url"
RTE_DefaultConfig.text_upload = "muat naik";	//"Upload"
RTE_DefaultConfig.text_size = "saiz";	//"Size"
RTE_DefaultConfig.text_text = "teks";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Buka dalam tab baru";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "masukkan";	//"Insert"
RTE_DefaultConfig.text_update = "kemaskini terakhir";	//"Update"
RTE_DefaultConfig.text_find = "Cari & ganti";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "cari";	//"Find"
RTE_DefaultConfig.text_replacewith = "ganti";	//"Replace"
RTE_DefaultConfig.text_findnext = "seterusnya";	//"Next"
RTE_DefaultConfig.text_replaceonce = "ganti";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Menggantikan semua";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Kes perlawanan";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Padanan perkataan";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Alih ke bawah";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Bergerak ke atas";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Saiz auto";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% lebar";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% lebar";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% lebar";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% lebar";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Saiz set";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Teks Alt";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "mewajarkan";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Kapsyen imej";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Menggabungkan sel";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Sel Split menegak";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Sel Split mendatar";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Warna teks sel";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Warna belakang sel";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Masukkan baris di atas";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Masukkan baris di bawah";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Masukkan lajur kiri";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Masukkan lajur kanan";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Padamkan lajur";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Padamkan baris";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Hapus jadual";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Saiz auto";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Pengepala Jadual";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Tambah perenggan baharu";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "tampal";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "tampal";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Tampal teks";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Tampal sebagai HTML";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Tampal perkataan";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Sila gunakan CTRL + V untuk menampal kandungan ke dalam kotak di bawah. \R\nkandungan akan dibersihkan secara automatik.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "perenggan";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "perenggan";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Bergerak ke atas";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Alih ke bawah";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "pendua";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "menghapuskan";	//"Delete"
RTE_DefaultConfig.text_pmore = "Lebih banyak..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Lebih banyak..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Sempadan Togol";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "potong";	//"Cut"
RTE_DefaultConfig.text_copy = "menyalin";	//"Copy"
RTE_DefaultConfig.text_copied = "disalin";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Sisipkan Galeri";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Masukkan dokumen";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Sisipkan templat";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "pratonton";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "biasa";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mudah alih";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "Tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "jadual";	//"Table"
RTE_DefaultConfig.text_tablecell = "Sel Jadual";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Baris Jadual";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Lajur Jadual";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatik";	//"Automatic"
RTE_DefaultConfig.text_colormore = "lebih banyak";	//"More"
RTE_DefaultConfig.text_colorpicker = "PEMILIH warna";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Pelet web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Warna yang dinamakan";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "asas";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "samping itu";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Drag and drop";	//"Drag and drop"
RTE_DefaultConfig.text_or = "atau";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Klik untuk memuat naik";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Kapsyen imej lalai";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "carian";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Teks yang akan ditambah telah mencapai had aksara untuk medan ini.";	//"The text to be added has reached the character limit for this field."

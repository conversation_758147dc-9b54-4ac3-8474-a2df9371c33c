﻿//Dutch , Nederlands
RTE_DefaultConfig.text_language = "language";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "annuleren";	//"Cancel"
RTE_DefaultConfig.text_normal = "normale";	//"Normal"
RTE_DefaultConfig.text_h1 = "Kop 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Kop 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Kop 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Kop 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Kop 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Kop 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Kop 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "sluiten";	//"Close"
RTE_DefaultConfig.text_bold = "vet";	//"Bold"
RTE_DefaultConfig.text_italic = "cursief";	//"Italic"
RTE_DefaultConfig.text_underline = "onderstreping";	//"Underline"
RTE_DefaultConfig.text_strike = "Strike Line";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "superscript";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Hoofdletters";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Kleine letters";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Opmaak verwijderen";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Koppeling invoegen";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Koppeling openen";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Koppeling bewerken";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Koppeling verwijderen";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Lijnhoogte";	//"Line Height"
RTE_DefaultConfig.text_indent = "streepje";	//"Indent"
RTE_DefaultConfig.text_outdent = "inspringing";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Offerte blokkeren";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Geordende lijst";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Ongeordende lijst";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Horizontale regel invoegen";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Datum invoegen";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Tabel invoegen";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Afbeelding invoegen";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Video invoegen";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Code invoegen";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF maken";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Emoji invoegen";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Speciale tekens";	//"Special characters"
RTE_DefaultConfig.text_characters = "tekens";	//"Characters"
RTE_DefaultConfig.text_fontname = "lettertype";	//"Font"
RTE_DefaultConfig.text_fontsize = "grootte";	//"Size"
RTE_DefaultConfig.text_forecolor = "Tekstkleur";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Achterkleur";	//"Back Color"
RTE_DefaultConfig.text_justify = "rechtvaardigen";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Links uitvullen";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Rechtvaardig rechts";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Center uitvullen";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Volledig uitvullen";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Geen uitvullen";	//"Justify None"
RTE_DefaultConfig.text_delete = "verwijderen";	//"Delete"
RTE_DefaultConfig.text_save = "Bestand opslaan";	//"Save file"
RTE_DefaultConfig.text_selectall = "Alles selecteren";	//"Select All"
RTE_DefaultConfig.text_code = "HTML-code";	//"HTML Code"
RTE_DefaultConfig.text_preview = "preview";	//"Preview"
RTE_DefaultConfig.text_print = "afdrukken";	//"Print"
RTE_DefaultConfig.text_undo = "ongedaan maken";	//"Undo"
RTE_DefaultConfig.text_redo = "opnieuw";	//"Redo"
RTE_DefaultConfig.text_more = "Meer...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Nieuw document";	//"New Doc"
RTE_DefaultConfig.text_help = "helpen";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Geschikt voor venster";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Volledig scherm afsluiten";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Afbeeldingseditor";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Afbeeldingsstijlen";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Inline-stijlen";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Alineastijlen";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Koppelingsstijlen";	//"Link Styles"
RTE_DefaultConfig.text_link = "link";	//"Link"
RTE_DefaultConfig.text_style = "stijlen";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css-klassen";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Op url";	//"By Url"
RTE_DefaultConfig.text_upload = "uploaden";	//"Upload"
RTE_DefaultConfig.text_size = "grootte";	//"Size"
RTE_DefaultConfig.text_text = "tekst";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Openen op nieuw tabblad";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "invoegen";	//"Insert"
RTE_DefaultConfig.text_update = "update";	//"Update"
RTE_DefaultConfig.text_find = "Zoeken&vervang";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "vinden";	//"Find"
RTE_DefaultConfig.text_replacewith = "vervangen";	//"Replace"
RTE_DefaultConfig.text_findnext = "volgende";	//"Next"
RTE_DefaultConfig.text_replaceonce = "vervangen";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Alles vervangen";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Kwestie overeenkomen";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Woord overeenkomen";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Omlaag gaan";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Omhoog gaan";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Automatische grootte";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% breedte";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% breedte";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% breedte";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% breedte";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Grootte instellen";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alternatieve tekst";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "rechtvaardigen";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Afbeeldingsbijschrift";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Cellen samenvoegen";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Cellen verticaal splitsen";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Cellen horizontaal splitsen";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Celtekstkleur";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Cel achterkleur";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Rij boven invoegen";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Rij hieronder invoegen";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Kolom links invoegen";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Kolomrecht invoegen";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Kolom verwijderen";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Rij verwijderen";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Tabel verwijderen";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Automatische grootte";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Tabelkoptekst";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Een nieuwe alinea toevoegen";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "plakken";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "plakken";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Tekst plakken";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Plakken als Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Word plakken";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Gebruik Ctrl+V om de inhoud in het onderstaande vak te plakken. \r\nDe inhoud wordt automatisch gereinigd.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "paragrafen";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "paragrafen";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Omhoog gaan";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Omlaag gaan";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "dupliceren";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "verwijderen";	//"Delete"
RTE_DefaultConfig.text_pmore = "Meer..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Meer..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Grens in- of uitschakelen";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "knippen";	//"Cut"
RTE_DefaultConfig.text_copy = "kopiëren";	//"Copy"
RTE_DefaultConfig.text_copied = "gekopieerd";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Galerie invoegen";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Document invoegen";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Sjabloon invoegen";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "preview";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normale";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobiele";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "tabel";	//"Table"
RTE_DefaultConfig.text_tablecell = "Tabelcel";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Tabelrij";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Tabelkolom";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatische";	//"Automatic"
RTE_DefaultConfig.text_colormore = "meer";	//"More"
RTE_DefaultConfig.text_colorpicker = "Kleurkiezer";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Webpalet";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Benoemde kleuren";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "basic";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "toevoeging";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Slepen en neerzetten";	//"Drag and drop"
RTE_DefaultConfig.text_or = "of";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Klik om te uploaden";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Standaardafbeeldingsbijschrift";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "zoek";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "De toe te voegen tekst heeft de tekenlimiet voor dit veld bereikt.";	//"The text to be added has reached the character limit for this field."

﻿//Portuguese (Portugal) , Português (Portugal)
RTE_DefaultConfig.text_language = "Idioma";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "Cancelar";	//"Cancel"
RTE_DefaultConfig.text_normal = "Normal";	//"Normal"
RTE_DefaultConfig.text_h1 = "Manchete 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Manchete 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Manchete 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Manchete 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Manchete 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Manchete 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Manchete 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "Fechar";	//"Close"
RTE_DefaultConfig.text_bold = "Arrojado";	//"Bold"
RTE_DefaultConfig.text_italic = "Itálico";	//"Italic"
RTE_DefaultConfig.text_underline = "Sublinhar";	//"Underline"
RTE_DefaultConfig.text_strike = "Linha de Greve";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "Superscript";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Caso Superior";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Minúscula inferior";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Remover formato";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Insira link";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Link aberto";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Editar Link";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Remover link";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Altura da linha";	//"Line Height"
RTE_DefaultConfig.text_indent = "Travessão";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Cotação do Bloco";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Lista ordenada";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Lista não ordenada";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Inserir regra horizontal";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Inserir Data";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Inserir Tabela";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Inserir Imagem";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Inserir Vídeo";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Inserir Código";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Criar PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Inserir Emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Personagens especiais";	//"Special characters"
RTE_DefaultConfig.text_characters = "Personagens";	//"Characters"
RTE_DefaultConfig.text_fontname = "Fonte";	//"Font"
RTE_DefaultConfig.text_fontsize = "Tamanho";	//"Size"
RTE_DefaultConfig.text_forecolor = "Cor de texto";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Cor traseira";	//"Back Color"
RTE_DefaultConfig.text_justify = "Justificar";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Justificar esquerda";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Justificar Direito";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Centro de Justificação";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Justificar Full";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Não justifique nenhuma";	//"Justify None"
RTE_DefaultConfig.text_delete = "Excluir";	//"Delete"
RTE_DefaultConfig.text_save = "Guardar ficheiro";	//"Save file"
RTE_DefaultConfig.text_selectall = "Selecione Tudo";	//"Select All"
RTE_DefaultConfig.text_code = "Código HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "previsualizar";	//"Preview"
RTE_DefaultConfig.text_print = "Imprimir";	//"Print"
RTE_DefaultConfig.text_undo = "Desfazer";	//"Undo"
RTE_DefaultConfig.text_redo = "Redo";	//"Redo"
RTE_DefaultConfig.text_more = "Mais...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Novo Doc";	//"New Doc"
RTE_DefaultConfig.text_help = "Ajuda";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Ajuste à janela";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Sair Ecrã Completo";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Editor de Imagem";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Estilos de imagem";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Estilos Inline";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Estilos de parágrafo";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Estilos de ligação";	//"Link Styles"
RTE_DefaultConfig.text_link = "Ligação";	//"Link"
RTE_DefaultConfig.text_style = "Estilos";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Aulas de CSS";	//"Css Classes"
RTE_DefaultConfig.text_url = "Url";	//"Url"
RTE_DefaultConfig.text_byurl = "Por Url";	//"By Url"
RTE_DefaultConfig.text_upload = "Upload";	//"Upload"
RTE_DefaultConfig.text_size = "Tamanho";	//"Size"
RTE_DefaultConfig.text_text = "Texto";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Abrir em novo separador";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "Inserir";	//"Insert"
RTE_DefaultConfig.text_update = "Atualização";	//"Update"
RTE_DefaultConfig.text_find = "Localizar e Substituir";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "Encontrar";	//"Find"
RTE_DefaultConfig.text_replacewith = "Substituir";	//"Replace"
RTE_DefaultConfig.text_findnext = "A seguir";	//"Next"
RTE_DefaultConfig.text_replaceonce = "Substituir";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Substituir tudo";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Caso de jogo";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Palavra de jogo";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Mover-se para baixo";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Subir";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Tamanho auto";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% de largura";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% de largura";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% de largura";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% de largura";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Tamanho do conjunto";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Texto alt";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "Justificar";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Legenda da imagem";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Células de fusão";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Células divididas Verticais";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Células divididas horizontal";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Cor de texto celular";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Cor celular de volta";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Inserir linha acima";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Inserir linha abaixo";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Inserir coluna à esquerda";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Inserir Coluna Direita";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Excluir coluna";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Excluir linha";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Excluir Tabela";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Tamanho auto";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Cabeçalho de mesa";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Adicione um novo parágrafo";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "Pasta";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "Pasta";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Texto de pasta";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Pasta como Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Palavra pasta";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Utilize CTRL+V para colar o conteúdo na caixa abaixo. \r\nO conteúdo será limpo automaticamente.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "Parágrafos";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "Parágrafos";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Subir";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Mover-se para baixo";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "Duplicado";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "Excluir";	//"Delete"
RTE_DefaultConfig.text_pmore = "Mais.";	//"More.."
RTE_DefaultConfig.text_togglemore = "Mais.";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Fronteira toggle";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "Corte";	//"Cut"
RTE_DefaultConfig.text_copy = "Cópia";	//"Copy"
RTE_DefaultConfig.text_copied = "copiado";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Inserir Galeria";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Inserir Documento";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Modelo de inserção";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "previsualizar";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "Normal";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "Móvel";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "Tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "Tabela";	//"Table"
RTE_DefaultConfig.text_tablecell = "Célula de mesa";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Linha de Mesa";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Coluna de Tabela";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "Automático";	//"Automatic"
RTE_DefaultConfig.text_colormore = "Mais";	//"More"
RTE_DefaultConfig.text_colorpicker = "Picker de cor";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Paleta Web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Cores Nomeadas";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "Básico";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "Adição";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Arrastar e largar";	//"Drag and drop"
RTE_DefaultConfig.text_or = "ou";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Clique para carregar";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Legenda de imagem padrão";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "Pesquisar";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "O texto a adicionar atingiu o limite de caracteres para este campo.";	//"The text to be added has reached the character limit for this field."

﻿//Portuguese (Brazil) , Português (Brasil)
RTE_DefaultConfig.text_language = "língua";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "cancelar";	//"Cancel"
RTE_DefaultConfig.text_normal = "normal";	//"Normal"
RTE_DefaultConfig.text_h1 = "Manchete 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Manchete 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Manchete 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Manchete 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Manchete 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Manchete 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Manchete 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "fechar";	//"Close"
RTE_DefaultConfig.text_bold = "negrito";	//"Bold"
RTE_DefaultConfig.text_italic = "itálico";	//"Italic"
RTE_DefaultConfig.text_underline = "sublinhado";	//"Underline"
RTE_DefaultConfig.text_strike = "Linha de Ataque";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "sobrescrito";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Maiúsculas";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Caixa Inferior";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Remover formato";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Inserir link";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Abrir link";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Editar link";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Remover link";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Altura da linha";	//"Line Height"
RTE_DefaultConfig.text_indent = "travessão";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Cotação do bloco";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Lista ordenada";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Lista não ordenada";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Inserir regra horizontal";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Data de inserção";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Inserir tabela";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Inserir imagem";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Inserir vídeo";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Código de inserção";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Criar PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Inserir emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Personagens especiais";	//"Special characters"
RTE_DefaultConfig.text_characters = "caracteres";	//"Characters"
RTE_DefaultConfig.text_fontname = "fonte";	//"Font"
RTE_DefaultConfig.text_fontsize = "tamanho";	//"Size"
RTE_DefaultConfig.text_forecolor = "Cor do texto";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Cor da parte de trás";	//"Back Color"
RTE_DefaultConfig.text_justify = "justificar";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Justificar esquerda";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Justificar o direito";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Centro de Justificação";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Justificar completo";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Justificar Nenhum";	//"Justify None"
RTE_DefaultConfig.text_delete = "excluir";	//"Delete"
RTE_DefaultConfig.text_save = "Salvar arquivo";	//"Save file"
RTE_DefaultConfig.text_selectall = "Selecione Todos";	//"Select All"
RTE_DefaultConfig.text_code = "Código HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "visualização";	//"Preview"
RTE_DefaultConfig.text_print = "imprimir";	//"Print"
RTE_DefaultConfig.text_undo = "desfazer";	//"Undo"
RTE_DefaultConfig.text_redo = "refazer";	//"Redo"
RTE_DefaultConfig.text_more = "Mais...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Novo Doutor";	//"New Doc"
RTE_DefaultConfig.text_help = "ajudar";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Ajuste à janela";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Saia da tela cheia";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Editor de imagens";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Estilos de imagem";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Estilos Inline";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Estilos de parágrafo";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Estilos de link";	//"Link Styles"
RTE_DefaultConfig.text_link = "link";	//"Link"
RTE_DefaultConfig.text_style = "estilos";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Aulas de Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "Por Url";	//"By Url"
RTE_DefaultConfig.text_upload = "carregar";	//"Upload"
RTE_DefaultConfig.text_size = "tamanho";	//"Size"
RTE_DefaultConfig.text_text = "texto";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Abra em nova guia";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "inserir";	//"Insert"
RTE_DefaultConfig.text_update = "atualização";	//"Update"
RTE_DefaultConfig.text_find = "Find&Replace";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "encontrar";	//"Find"
RTE_DefaultConfig.text_replacewith = "substituir";	//"Replace"
RTE_DefaultConfig.text_findnext = "próximo";	//"Next"
RTE_DefaultConfig.text_replaceonce = "substituir";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Substituir todos";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Caso de correspondência";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Palavra de correspondência";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Mova-se para baixo";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Mova-se para cima";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Tamanho automático";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% de largura";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% de largura";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% de largura";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% de largura";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Tamanho do conjunto";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Texto alt";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "justificar";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Legenda da imagem";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Mesclar células";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Células divididas verticais";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Células divididas horizontais";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Cor do texto da célula";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Cor da parte traseira do celular";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Inserir linha acima";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Inserir linha abaixo";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Inserir coluna à esquerda";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Inserir a coluna direita";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Excluir coluna";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Excluir linha";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Excluir tabela";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Tamanho automático";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Cabeçalho da tabela";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Adicione um novo parágrafo";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "colar";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "colar";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Texto de colar";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Colar como Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Palavra de colar";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Por favor, use CTRL+V para colar o conteúdo na caixa abaixo. \r\nO conteúdo será limpo automaticamente.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "parágrafos";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "parágrafos";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Mova-se para cima";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Mova-se para baixo";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "duplicar";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "excluir";	//"Delete"
RTE_DefaultConfig.text_pmore = "Mais..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Mais..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Alternar fronteira";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "cortar";	//"Cut"
RTE_DefaultConfig.text_copy = "cópia";	//"Copy"
RTE_DefaultConfig.text_copied = "copiado";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Galeria de Inserção";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Inserir documento";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Modelo de inserção";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "visualização";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normal";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "móvel";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "tabela";	//"Table"
RTE_DefaultConfig.text_tablecell = "Célula de Mesa";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Linha de mesa";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Coluna da Tabela";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automático";	//"Automatic"
RTE_DefaultConfig.text_colormore = "mais";	//"More"
RTE_DefaultConfig.text_colorpicker = "Catador de cores";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Paleta Web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Cores nomeadas";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "basic";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "adição";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Arrastar e soltar";	//"Drag and drop"
RTE_DefaultConfig.text_or = "ou";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Clique para carregar";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Legenda de imagem padrão";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "busca";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "O texto a ser adicionado atingiu o limite de caracteres para este campo.";	//"The text to be added has reached the character limit for this field."

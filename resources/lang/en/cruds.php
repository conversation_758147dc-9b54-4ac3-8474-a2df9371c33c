<?php

return [
    'userManagement' => [
        'title'          => 'User management',
        'title_singular' => 'User management',
    ],
    'permission' => [
        'title'          => 'Permissions',
        'title_singular' => 'Permission',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'title'             => 'Title',
            'title_helper'      => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'role' => [
        'title'          => 'Roles',
        'title_singular' => 'Role',
        'fields'         => [
            'id'                 => 'ID',
            'id_helper'          => ' ',
            'title'              => 'Title',
            'title_helper'       => ' ',
            'permissions'        => 'Permissions',
            'permissions_helper' => ' ',
            'created_at'         => 'Created at',
            'created_at_helper'  => ' ',
            'updated_at'         => 'Updated at',
            'updated_at_helper'  => ' ',
            'deleted_at'         => 'Deleted at',
            'deleted_at_helper'  => ' ',
        ],
    ],
    'user' => [
        'title'          => 'Users',
        'title_singular' => 'User',
        'fields'         => [
            'id'                       => 'ID',
            'id_helper'                => ' ',
            'name'                     => 'Name',
            'name_helper'              => ' ',
            'email'                    => 'Email',
            'email_helper'             => ' ',
            'email_verified_at'        => 'Email verified at',
            'email_verified_at_helper' => ' ',
            'password'                 => 'Password',
            'password_helper'          => ' ',
            'roles'                    => 'Roles',
            'roles_helper'             => ' ',
            'remember_token'           => 'Remember Token',
            'remember_token_helper'    => ' ',
            'created_at'               => 'Created at',
            'created_at_helper'        => ' ',
            'updated_at'               => 'Updated at',
            'updated_at_helper'        => ' ',
            'deleted_at'               => 'Deleted at',
            'deleted_at_helper'        => ' ',
        ],
    ],
    'page' => [
        'title'          => 'Page',
        'title_singular' => 'Page',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'title'             => 'Title',
            'title_helper'      => ' ',
            'slug'              => 'Slug',
            'slug_helper'       => ' ',
            'content'           => 'Content',
            'content_helper'    => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
            'meta_description'  => 'Meta Description',
            'meta_description_helper' => ' ',
            'meta_keywords'     => 'Meta Keywords',
            'meta_keywords_helper' => ' ',
        ],
    ],
    'setting' => [
        'title'          => 'Settings',
        'title_singular' => 'Setting',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'meta_key'          => 'Meta Key',
            'meta_key_helper'   => ' ',
            'meta_value'        => 'Meta Value',
            'meta_value_helper' => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'seo' => [
        'title'          => 'Seo',
        'title_singular' => 'Seo',
        'fields'         => [
            'id'                       => 'ID',
            'id_helper'                => ' ',
            'meta_title'               => 'Meta Title',
            'meta_title_helper'        => ' ',
            'meta_description'         => 'Meta Description',
            'meta_description_helper'  => ' ',
            'meta_keywords'            => 'Meta Keywords',
            'meta_keywords_helper'     => ' ',
            'canonical_url'            => 'Canonical Url',
            'canonical_url_helper'     => ' ',
            'og_image'                 => 'Og Image',
            'og_image_helper'          => ' ',
            'og_type'                  => 'Og Type',
            'og_type_helper'           => ' ',
            'schema_type'              => 'Schema Type',
            'schema_type_helper'       => ' ',
            'schema_json'              => 'Schema Json',
            'schema_json_helper'       => ' ',
            'sitemap_priority'         => 'Sitemap Priority',
            'sitemap_priority_helper'  => ' ',
            'sitemap_frequency'        => 'Sitemap Frequency',
            'sitemap_frequency_helper' => ' ',
            'page'                     => 'Page',
            'page_helper'              => ' ',
            'created_at'               => 'Created at',
            'created_at_helper'        => ' ',
            'updated_at'               => 'Updated at',
            'updated_at_helper'        => ' ',
            'deleted_at'               => 'Deleted at',
            'deleted_at_helper'        => ' ',
            'index'                    => 'Index',
            'index_helper'             => ' ',
            'follow'                   => 'Follow',
            'follow_helper'            => ' ',
        ],
    ],
    'section' => [
        'title'          => 'Section',
        'title_singular' => 'Section',
        'fields'         => [
            'id'                  => 'ID',
            'id_helper'           => ' ',
            'page'                => 'Page',
            'page_helper'         => ' ',
            'section_type'        => 'Section Type',
            'section_type_helper' => ' ',
            'title'               => 'Title',
            'title_helper'        => ' ',
            'text'                => 'Text',
            'text_helper'         => ' ',
            'image'               => 'Image',
            'image_helper'        => ' ',
            'google_url'          => 'Google Url',
            'google_url_helper'   => ' ',
            'sort_order'          => 'Sort Order',
            'sort_order_helper'   => ' ',
            'created_at'          => 'Created at',
            'created_at_helper'   => ' ',
            'updated_at'          => 'Updated at',
            'updated_at_helper'   => ' ',
            'deleted_at'          => 'Deleted at',
            'deleted_at_helper'   => ' ',
        ],
    ],
    'sectionItem' => [
        'title'          => 'Section Item',
        'title_singular' => 'Section Item',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'section'           => 'Section',
            'section_helper'    => ' ',
            'title'             => 'Title',
            'title_helper'      => ' ',
            'url'               => 'Url',
            'url_helper'        => ' ',
            'image'             => 'Image',
            'image_helper'      => ' ',
            'content'           => 'Content',
            'content_helper'    => ' ',
            'sort_order'        => 'Sort Order',
            'sort_order_helper' => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],
    'contactMessage' => [
        'title'          => 'Contact Message',
        'title_singular' => 'Contact Message',
        'fields'         => [
            'id'                => 'ID',
            'id_helper'         => ' ',
            'name'              => 'Name',
            'name_helper'       => ' ',
            'email'             => 'Email',
            'email_helper'      => ' ',
            'message'           => 'Message',
            'message_helper'    => ' ',
            'status'            => 'Status',
            'status_helper'     => ' ',
            'created_at'        => 'Created at',
            'created_at_helper' => ' ',
            'updated_at'        => 'Updated at',
            'updated_at_helper' => ' ',
            'deleted_at'        => 'Deleted at',
            'deleted_at_helper' => ' ',
        ],
    ],

];

@extends('layouts.admin')

@section('header_title')
    {{ trans('global.show') }} {{ trans('cruds.contactMessage.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container"  >
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.contactMessage.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="index.html" class="text-muted text-hover-primary">
                            {{ trans('cruds.contactMessage.title_singular') }}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">
                        {{ trans('global.show') }} {{ trans('cruds.contactMessage.title_singular') }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">

        <div id="kt_app_content" class="app-content flex-column-fluid">

            <div id="kt_app_content_container" class="container-xxxl">

                <div class="row g-0">
                    <div class="col-lg-6 col-6 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4> {{ trans('global.show') }} {{ trans('cruds.contactMessage.title_singular') }}</h4>
                            </div>
                            <div class="card-body p-0">
                                <div class="form-group">

                                    <table class="table table-bordered table-striped">
                                        <tbody>
                                            <tr>
                                                <th>
                                                    {{ trans('cruds.contactMessage.fields.id') }}
                                                </th>
                                                <td>
                                                    {{ $contactMessage->id }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>
                                                    {{ trans('cruds.contactMessage.fields.name') }}
                                                </th>
                                                <td>
                                                    {{ $contactMessage->name }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>
                                                    {{ trans('cruds.contactMessage.fields.email') }}
                                                </th>
                                                <td>
                                                    {{ $contactMessage->email }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>
                                                    {{ trans('cruds.contactMessage.fields.message') }}
                                                </th>
                                                <td>
                                                    {{ $contactMessage->message }}
                                                </td>
                                            </tr>
                                            
                                        </tbody>
                                    </table>
                                    <div class="form-group">
                                        <a class="btn btn-primary btn-sm" href="{{ route('admin.contact-messages.index') }}">
                                            {{ trans('global.back_to_list') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>
@endsection

@section('scripts')
@endsection

{{-- gallery modal --}}
<button data-modal-target="extralarge-modal" data-modal-toggle="extralarge-modal" class="open-gallery-button hidden" type="button">Extra large modal</button>

<div id="extralarge-modal" tabindex="-1" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-4xl max-h-full">

        <div class="relative bg-white rounded-lg shadow-sm ">

            <div class="flex items-center justify-between p-2 md:p-2 border-b rounded-t  border-gray-200">
                <h6 class="text-sm font-medium text-gray-900 ">
                    Photo Gallery
                </h6>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="extralarge-modal">
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>

            <div class="p-4 md:p-5 space-y-4 overflow-y-auto" id="gallery-modal-body" style="height: 56vh;">

            </div>

            <div class="flex items-center p-4 md:p-5 space-x-3 rtl:space-x-reverse border-t border-gray-200 rounded-b ">

            </div>
        </div>
    </div>
</div>
{{-- gallery modal end --}}

{{-- gallery modal script --}}
<script>
    $(document).ready(function() {

        // $('.open-gallery-button').on('click', function(e) {
        //     e.preventDefault();
        //     let url = "{{ route('admin.galleries.index', $page->id) }}";
        //     loadGallery(url);
        // });

        $(document).on('submit', '#gallery-modal-body #gallery-search-form', function(e) {
            e.preventDefault();
            let formData = $(this).serialize();
            let url = "{{ route('admin.galleries.index', $page->id) }}?" + formData;
            loadGallery(url);
        });

        $(document).on('click', '#gallery-modal-body .pagination a', function(e) {
            e.preventDefault();
            let url = $(this).attr('href');
            loadGallery(url);
        });

        $(document).on('click', '#search-upload-gallery', function(e) {
            e.preventDefault();
            $('#hidden-upload-input').trigger('click');
        });

        $(document).on('change', '#hidden-upload-input', function(e) {
            let file = this.files[0];
            if (!file) return;

            showLoadingSpinner();

            let formData = new FormData();
            formData.append('file', file);
            formData.append('_token', "{{ csrf_token() }}");

            $.ajax({
                url: "{{ route('admin.galleries.upload', $page->id) }}",
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    loadGallery("{{ route('admin.galleries.index', $page->id) }}");
                },
                error: function(xhr) {
                    $('#gallery-modal-body').html(xhr.responseText);
                }
            });
        });

        function loadGallery(url) {
            showLoadingSpinner();
            $.ajax({
                url: url,
                method: 'GET',
                dataType: 'html',
                success: function(response) {
                    $('#gallery-modal-body').html(response);
                },
                error: function(xhr) {
                    console.log('Error:', xhr.responseText);
                }
            });
        }

        function showLoadingSpinner() {
            $("#gallery-modal-body").html(
                '<div class="flex justify-center items-center h-48">' +
                '<div class="w-8 h-8 border-4 border-blue-500 border-t-transparent border-solid rounded-full animate-spin"></div>' +
                '</div>'
            );
        }

        window.copyUrl = function(url, container) {

            if (navigator.clipboard) {
                navigator.clipboard.writeText(url)
                    .then(() => setCopiedText(container))
                    .catch(err => console.error('Failed to copy: ', err));
            } else {
                const input = document.createElement('input');
                input.value = url;
                document.body.appendChild(input);
                input.select();
                document.execCommand('copy');
                document.body.removeChild(input);
                setCopiedText(container);
            }
        }

        function setCopiedText(container) {
            const overlay = container.querySelector('[data-copy-text]');
            if (!overlay) return;

            overlay.textContent = 'Copied!';

            function restoreText() {
                overlay.textContent = 'Click to copy image url';
                container.removeEventListener('mouseleave', restoreText);
            }

            container.addEventListener('mouseleave', restoreText);
        }

        window.selectedGalleryInput = null;

        $(document).on('click', '[data-gallery]', function(e) {
            e.preventDefault();

            window.selectedGalleryInput = $(this);

            $('.open-gallery-button').trigger('click');
            let url = "{{ route('admin.galleries.index', $page->id) }}";
            loadGallery(url);

        });

        $(document).on('click', '#gallery-modal-body .border.rounded[data-image-url]', function(e) {
            e.preventDefault();
            let imageUrl = $(this).attr('data-image-url');

            if (window.selectedGalleryInput) {
                window.selectedGalleryInput.val(imageUrl);
            }

            $('[data-modal-hide="extralarge-modal"]').trigger('click');
        });


    });
</script>
{{-- gallery modal script end --}}

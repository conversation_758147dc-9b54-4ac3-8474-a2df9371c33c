<div class=" bg-white border-r flex flex-col shadow-lg" id="drawer">
    <div class="p-4 border-b flex items-center justify-between">
        <h6 class="text-sm font-semibold text-gray-500 uppercase">
            Modifier Section
        </h6>
        <label for="Toggle3" class="inline-flex items-center rounded-md cursor-pointer dark:text-gray-100 me-3">
            <input id="Toggle3" type="checkbox" class="hidden peer" {{ $language === 'en' ? 'checked' : '' }}>
            <span data-language="fr" class="px-2 py-1 rounded-l-md text-xs {{ $language === 'fr' ? 'dark:bg-violet-600' : 'dark:bg-gray-700' }}">FR</span>
            <span data-language="en" class="px-2 py-1 rounded-r-md text-xs {{ $language === 'en' ? 'dark:bg-violet-600' : 'dark:bg-gray-700' }}">EN</span>
        </label>
    </div>

    <form id="drawer-form" class="p-2 flex-grow overflow-y-auto " action="{{ route($update_url) }}" method="POST">
        @csrf
        @method('PUT')

        <div id="drawer-content" class="no-scrollbar p-1">



        </div>

        <div class="border-t pt-4">
            <div class="flex justify-between space-x-2">
                <button type="reset" class="px-2 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-100 text-xs">
                    Cancel
                </button>
                <button type="submit" class="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs">
                    Update
                </button>
            </div>
        </div>
    </form>
</div>

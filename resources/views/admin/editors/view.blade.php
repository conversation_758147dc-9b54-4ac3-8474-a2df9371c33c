<!DOCTYPE html>
<html lang="en">

@include('admin.editors.inc.head')

<body class="bg-gray-100 flex">

    @include('admin.editors.inc.sidebar')

    <div class="bg-[url('{{ $bg_url }}')] bg-cover bg-center @if ($page->id == 4 || $page->id == 5) bg-top @endif min-h-screen flex flex-col flex-grow relative" class="" id="page-content">


        @include('frontend.section.header', ['section_header' => $section_header])

        @if ($page->id == 1 || $page->id == 2 || $page->id == 3)
            <main class="flex-1 flex justify-end items-center px-4 sm:px-6 lg:px-24">
        @elseif ($page->id == 6 || $page->id == 10)
            <main class="min-h-screen pt-10 container-inner">
        @else
            <main class="min-h-screen container-inner">
        @endif
        @include('frontend.page.' . $admin_page, ['section_data' => $section_data])
        @include('frontend.section.footer', ['section_footer' => $section_footer])
        </main>

    </div>

    @include('admin.editors.inc.gallery')
    @include('admin.editors.inc.language')

    {{-- drawer form script --}}
    <script>
        $(function() {

            window.onload = function() {
                const section = @json($section ?? '');
                if (section) {
                    const target = document.querySelector(`[data-section="${section}"]`);
                    if (target) {
                        target.scrollIntoView({
                            behavior: "smooth",
                            block: "start"
                        });

                    }
                }
                drawerForm('{{ $section }}', '{{ $language }}');
                $("#drawerButton").trigger("click");
            };

            $("[data-section]").on("click", function(e) {
                e.preventDefault();
                let sectionValue = $(this).attr("data-section");
                $("#drawer-content").html(
                    '<div class="flex justify-center items-center h-48">' +
                    '<div class="w-8 h-8 border-4 border-blue-500 border-t-transparent border-solid rounded-full animate-spin"></div>' +
                    '</div>'
                );
                drawerForm(sectionValue, '{{ $language }}');
                $("#drawerButton").trigger("click");
            });

            $(document).on("click", "[data-drawer-dismiss]", function() {
                $("#drawer-content").html("");
            });

            function drawerForm(section, language) {
                $.ajax({
                    url: "{{ route('admin.pages.drawerForm') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        section: section,
                        page_id: "{{ $page->id }}",
                        language: language
                    },
                    success: function(data) {
                        $("#drawer-content").html(data.content);
                        initDrawerForm(data.section_name);
                    }
                });
            }

            function initDrawerForm(sectionName) {
                if (sectionName === "header") {
                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="URL" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list").sortable();
                }

                if (sectionName === "footer") {

                    ClassicEditor
                        .create(document.querySelector('#copyright_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });
                }

                if (sectionName === "index" || sectionName === "missions" || sectionName === "bureaux") {
                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Text" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                    $("#add-menu-item-1").on("click", function() {
                        $("#menu-list-1").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item-1 ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item-1", function() {
                        $(this).closest("li").remove();
                    });

                    $("#add-menu-item-2").on("click", function() {
                        $("#menu-list-2").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item-2 ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item-2", function() {
                        $(this).closest("li").remove();
                    });

                    $("#menu-list, #menu-list-1, #menu-list-2").sortable();

                }
                if (sectionName === "introduction") {
                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list").sortable();

                    ClassicEditor
                        .create(document.querySelector('#description_1_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_2_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_3_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_4_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });
                }
                if (sectionName === "professionnels") {
                    ClassicEditor
                        .create(document.querySelector('#description_1_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_2_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_3_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_4_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_5_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list").sortable();
                }
                if (sectionName === "assurances") {
                    ClassicEditor
                        .create(document.querySelector('#description_1_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_2_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_3_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_4_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_5_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_6_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    ClassicEditor
                        .create(document.querySelector('#description_7_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list").sortable();
                }
                if (sectionName === "localisations") {
                    ClassicEditor
                        .create(document.querySelector('#description_1_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_style" type="text" placeholder="Style" class="border rounded p-1 text-xs w-1/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list").sortable();

                    $("#add-menu-item-1").on("click", function() {
                        $("#menu-list-1").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item-1 ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item-1", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list-1").sortable();

                    $("#add-menu-item-2").on("click", function() {
                        $("#menu-list-2").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item-2 ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item-2", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list-2").sortable();


                }
                if (sectionName === "comores") {
                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list").sortable();

                    $("#add-menu-item-1").on("click", function() {
                        $("#menu-list-1").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item-1 ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item-1", function() {
                        $(this).closest("li").remove();
                    });
                    $("#menu-list-1").sortable();

                    ClassicEditor
                        .create(document.querySelector('#description_1_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });

                }


            }

            $("#drawer-form").on("submit", function() {
                var currentSection = $("#drawer-content").find('input[name="section"]').val();
                if (currentSection === "header") {
                    var menuData = [];
                    $("#menu-list li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#menu_data").val(JSON.stringify(menuData));

                    var socialData = [];
                    $("#social-list li").each(function() {
                        var url = $(this).find('input[name="social_url"]').val();
                        var img = $(this).find('input[name="social_image"]').val();
                        socialData.push({
                            url: url,
                            image: img
                        });
                    });
                    $("#social_data").val(JSON.stringify(socialData));

                }

                if (currentSection === "index" || currentSection === "missions" || currentSection === "bureaux") {
                    var menuData = [];
                    $("#menu-list li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_1_data").val(JSON.stringify(menuData));

                    var menuData_1 = [];
                    $("#menu-list-1 li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData_1.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_2_data").val(JSON.stringify(menuData_1));

                    var menuData_2 = [];
                    $("#menu-list-2 li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData_2.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_3_data").val(JSON.stringify(menuData_2));

                }

                if (currentSection === "introduction" || currentSection === "professionnels" || currentSection === "assurances") {
                    var menuData = [];
                    $("#menu-list li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_1_data").val(JSON.stringify(menuData));
                }

                if (currentSection === "localisations") {
                    var menuData = [];
                    $("#menu-list li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        var style = $(this).find('input[name="menu_style"]').val();
                        menuData.push({
                            title: title,
                            url: url,
                            style: style
                        });
                    });
                    $("#list_1_data").val(JSON.stringify(menuData));

                    var menuData_1 = [];
                    $("#menu-list-1 li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData_1.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_2_data").val(JSON.stringify(menuData_1));

                    var menuData_2 = [];
                    $("#menu-list-2 li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData_2.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_3_data").val(JSON.stringify(menuData_2));

                }

                if (currentSection === "comores") {
                    var menuData = [];
                    $("#menu-list li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_1_data").val(JSON.stringify(menuData));

                    var menuData_1 = [];
                    $("#menu-list-1 li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData_1.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_2_data").val(JSON.stringify(menuData_1));
                }


            });

        });
    </script>
    {{-- drawer form script end --}}

</body>

</html>

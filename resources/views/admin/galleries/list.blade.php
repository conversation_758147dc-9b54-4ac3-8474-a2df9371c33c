<form id="gallery-search-form" method="GET" action="" class="mb-4">
    @csrf
    <div class="flex space-x-2">
        <input type="text" name="search" value="{{ $search ?? '' }}" placeholder="Search images..." class="border rounded p-2 flex-grow text-xs" />
        <button id="search-button-gallery" type="submit" class="px-2 py-2 bg-blue-500 text-white rounded text-xs">
            Search
        </button>
        <button id="search-upload-gallery" type="button" class="px-2 py-2 bg-blue-500 text-white rounded text-xs">
            Upload
        </button>
        <input type="file" id="hidden-upload-input" class="hidden" accept="image/*" />
    </div>
</form>

<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    @forelse($media as $item)
        <div data-image-url="{{ $item->getUrl() }}" class="relative border rounded p-2 flex flex-col items-center hover:shadow cursor-pointer group" onclick="copyUrl('{{ $item->getUrl() }}', this)">
            <div data-copy-text class="absolute inset-0 flex items-center justify-center bg-black/50 text-white text-sm font-medium opacity-0 group-hover:opacity-100 transition">
                Click to copy image url
            </div>

            <img src="{{ $item->getUrl('thumb') ?? $item->getUrl() }}" alt="{{ $item->name }}" class="h-32 object-cover mb-2 w-full" />
            <div class="text-xs font-semibold truncate w-full">
                {{ $item->name }}
            </div>
            <div class="text-xs text-gray-500 truncate w-full">
                {{ $item->getUrl() }}
            </div>
        </div>

    @empty
        <p class="col-span-full text-center text-gray-500">
            No images found.
        </p>
    @endforelse
</div>


<div class="mt-4">
    {{ $media->links() }}
</div>

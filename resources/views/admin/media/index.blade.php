@extends('layouts.admin')

@section('header_title')
    Media Library
@endsection

@section('styles')
    <style>
        [data-copy-text] {
            z-index: 10;
        }

        [data-image-url]:hover [data-copy-text] {
            opacity: 1 !important;
        }
    </style>
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    Media Library
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="{{ route('admin.galleries.index', 1) }}" class="text-muted text-hover-primary">Media Library</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">Upload Files
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="container-xxxl">
                <div class="row g-0">
                    <div class="col-lg-12 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4>Media Library</h4>
                            </div>
                            <div class="card-body p-0">
                                <form id="gallery-search-form" method="GET" action="" class="mb-4">
                                    @csrf
                                    <div class="d-flex gap-2">
                                        <input type="text" name="search" value="{{ $search ?? '' }}" placeholder="Search images..." class="form-control form-control-sm flex-grow-1" />

                                        <button id="search-button-gallery" type="submit" class="btn btn-primary btn-sm">
                                            Search
                                        </button>

                                        <button id="search-upload-gallery" type="button" class="btn btn-primary btn-sm">
                                            Upload
                                        </button>

                                        <input type="file" id="hidden-upload-input" class="d-none" accept="image/*" />
                                    </div>
                                </form>

                                <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
                                    @forelse($media as $item)
                                        <div class="col">
                                            <div data-image-url="{{ $item->getUrl() }}" class="border rounded p-2 text-center position-relative" style="cursor: pointer;" onclick="copyUrl('{{ $item->getUrl() }}', this)">

                                                <!-- The hover overlay -->
                                                <div data-copy-text class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center text-white" style="background-color: rgba(0, 0, 0, 0.5); 
                                                            opacity: 0; 
                                                            transition: opacity 0.2s;">
                                                    Click to copy image url
                                                </div>

                                                <img src="{{ $item->getUrl('thumb') ?? $item->getUrl() }}" alt="{{ $item->name }}" class="w-100 mb-2" style="height: 8rem; object-fit: cover;" />

                                                <div class="fw-semibold small text-truncate">
                                                    {{ $item->name }}
                                                </div>
                                                <div class="text-muted small text-truncate">
                                                    {{ $item->getUrl() }}
                                                </div>
                                            </div>
                                        </div>
                                    @empty
                                        <p class="col-12 text-center text-muted">
                                            No images found.
                                        </p>
                                    @endforelse
                                </div>

                                <div class="mt-4">
                                    {{ $media->links('pagination::bootstrap-5') }}
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    {{-- gallery modal script --}}
    <script>
        $(document).ready(function() {

            function showLoadingSpinner() {
                $("#gallery-modal-body").html(
                    '<div class="flex justify-center items-center h-48">' +
                    '<div class="w-8 h-8 border-4 border-blue-500 border-t-transparent border-solid rounded-full animate-spin"></div>' +
                    '</div>'
                );
            }


            $(document).on('click', '#search-upload-gallery', function(e) {
                e.preventDefault();
                $('#hidden-upload-input').trigger('click');
            });

            $(document).on('change', '#hidden-upload-input', function(e) {
                let file = this.files[0];
                if (!file) return;

                showLoadingSpinner();

                let formData = new FormData();
                formData.append('file', file);
                formData.append('_token', "{{ csrf_token() }}");

                $.ajax({
                    url: "{{ route('admin.galleries.upload', 1) }}",
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        window.location.reload();
                    },
                    error: function(xhr) {
                        $('#gallery-modal-body').html(xhr.responseText);
                    }
                });
            });

            function setCopiedText(container) {
                const overlay = container.querySelector('[data-copy-text]');
                if (!overlay) return;

                // Change text to "Copied!"
                overlay.textContent = 'Copied!';

                // Restore original text when user moves mouse out
                function restoreText() {
                    overlay.textContent = 'Click to copy image url';
                    container.removeEventListener('mouseleave', restoreText);
                }
                container.addEventListener('mouseleave', restoreText);
            }

            window.copyUrl = function(url, container) {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url)
                        .then(() => setCopiedText(container))
                        .catch(err => console.error('Failed to copy: ', err));
                } else {
                    // Fallback for older browsers
                    const input = document.createElement('input');
                    input.value = url;
                    document.body.appendChild(input);
                    input.select();
                    document.execCommand('copy');
                    document.body.removeChild(input);
                    setCopiedText(container);
                }
            }
        });
    </script>
    {{-- gallery modal script end --}}
@endsection

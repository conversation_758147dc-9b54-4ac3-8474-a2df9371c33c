
<div class="mb-4">
    <label class="block text-xs font-semibold mb-1 text-center bg-gray-700 p-2 text-white">Section Comores</label>
</div>

<input type="hidden" name="section" value="comores">
<input type="hidden" name="list_1" id="list_1_data">
<input type="hidden" name="list_2" id="list_2_data">
<input type="hidden" name="language" id="language_data" value="{{ $language }}">

<!-- Image 1 -->
<div class="mb-4">
    <label class="block text-xs font-semibold mb-1">Comores Image </label>
    <input data-gallery="apropos" type="text" name="image_1" id="image_1_data" class="border border-gray-300 rounded w-full p-2 text-xs" value="{{ $section_data->image_1 }}">
</div>

<!-- Title 1 -->
<div class="mb-4">
    <label class="block text-xs font-semibold mb-1">Title 1</label>
    <input type="text" name="title_1" id="title_1_data" class="border border-gray-300 rounded w-full p-2 text-xs" value="{{ $section_data->title_1 }}">
</div>

<!-- Title 2 -->
<div class="mb-4">
    <label class="block text-xs font-semibold mb-1">Title 2</label>
    <input type="text" name="title_2" id="title_2_data" class="border border-gray-300 rounded w-full p-2 text-xs" value="{{ $section_data->title_2 }}">
</div>

<!-- Title 3 -->
<div class="mb-4">
    <label class="block text-xs font-semibold mb-1">Title 3</label>
    <input type="text" name="title_3" id="title_3_data" class="border border-gray-300 rounded w-full p-2 text-xs" value="{{ $section_data->title_3 }}">
</div>

<!-- Image 2 -->
<div class="mb-4">
    <label class="block text-xs font-semibold mb-1">Comores Background Image</label>
    <input data-gallery="apropos" type="text" name="image_2" id="image_2_data" class="border border-gray-300 rounded w-full p-2 text-xs" value="{{ $section_data->image_2 }}">
</div>

<!-- List 1 -->
<div class="mb-4">
    <div class="flex items-center justify-between mb-2">
        <h3 class="text-xs font-semibold">Action</h3>
        <button id="add-menu-item" type="button" class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 flex items-center">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path>
            </svg>
        </button>
    </div>
    <ul id="menu-list" class="border border-gray-200 p-2 rounded-md">
        @foreach ($section_data->list_1 as $value)
            <li class="mb-2 bg-gray-50 p-2 rounded flex items-center">
                <span type="button" class="me-1 cursor-pointer">=</span>
                <input type="text" name="menu_title" value="{{ $value['title'] }}" class="mr-2 border rounded p-1 text-xs w-1/3" />
                <input type="text" name="menu_url" value="{{ $value['url'] }}" class="border rounded p-1 text-xs w-2/3" />
                <button type="button" class="delete-menu-item ml-2">
                    <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </li>
        @endforeach
    </ul>
</div>

<!-- Description 1 -->
<div class="mb-4">
    <label class="block text-xs font-semibold mb-1">Description 1</label>
    <textarea name="description_1" id="description_1_data" class="border border-gray-300 rounded w-full p-2 text-xs" style="max-height: 200px" >{{ $section_data->description_1 }}</textarea>
</div>


<!-- List 2 -->
<div class="mb-4">
    <div class="flex items-center justify-between mb-2">
        <h3 class="text-xs font-semibold">Action</h3>
        <button id="add-menu-item-1" type="button" class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 flex items-center">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path>
            </svg>
        </button>
    </div>
    <ul id="menu-list-1" class="border border-gray-200 p-2 rounded-md">
        @foreach ($section_data->list_2 as $value)
            <li class="mb-2 bg-gray-50 p-2 rounded flex items-center">
                <span type="button" class="me-1 cursor-pointer">=</span>
                <input type="text" name="menu_title" value="{{ $value['title'] }}" class="mr-2 border rounded p-1 text-xs w-1/3" />
                <input type="text" name="menu_url" value="{{ $value['url'] }}" class="border rounded p-1 text-xs w-2/3" />
                <button type="button" class="delete-menu-item-1 ml-2">
                    <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </li>
        @endforeach
    </ul>
</div>










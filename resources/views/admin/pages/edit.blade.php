@extends('layouts.admin')

@section('header_title')
    {{ trans('global.edit') }} {{ trans('cruds.page.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.page.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="{{ route('admin.pages.index') }}" class="text-muted text-hover-primary">{{ trans('cruds.page.title_singular') }}</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">{{ trans('global.edit') }}
                        {{ trans('cruds.page.title_singular') }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="container-xxxl">
                <div class="row g-0">
                    <div class="col-lg-6 col-6 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4> {{ trans('global.edit') }} {{ trans('cruds.page.title_singular') }}</h4>
                            </div>
                            <div class="card-body p-0">
                                <form method="POST" action="{{ route('admin.pages.update', [$page->id]) }}" enctype="multipart/form-data">
                                    @method('PUT')
                                    @csrf
                                    <div class="row">
                                        <div class="form-group mb-3">
                                            <label class="form-label required" for="title">{{ trans('cruds.page.fields.title') }}</label>
                                            <input class="form-control form-control-sm {{ $errors->has('title') ? 'is-invalid' : '' }}" type="text" name="title" id="title" value="{{ old('title', $page->title) }}" required>
                                            @if ($errors->has('title'))
                                                <div class="invalid-feedback">
                                                    {{ $errors->first('title') }}
                                                </div>
                                            @endif
                                            <span class="help-block">{{ trans('cruds.page.fields.title_helper') }}</span>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label class="form-label required" for="slug">{{ trans('cruds.page.fields.slug') }}</label>
                                            <input class="form-control form-control-sm {{ $errors->has('slug') ? 'is-invalid' : '' }}" type="text" name="slug" id="slug" value="{{ old('slug', $page->slug) }}" required>
                                            @if ($errors->has('slug'))
                                                <div class="invalid-feedback">
                                                    {{ $errors->first('slug') }}
                                                </div>
                                            @endif
                                            <span class="help-block">{{ trans('cruds.page.fields.slug_helper') }}</span>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label class="form-label" for="meta_description">{{ trans('cruds.page.fields.meta_description') }}</label>
                                            <textarea class="form-control {{ $errors->has('meta_description') ? 'is-invalid' : '' }}" name="meta_description" id="meta_description">{{ old('meta_description', $page->meta_description) }}</textarea>
                                            @if($errors->has('meta_description'))
                                                <div class="invalid-feedback">
                                                    {{ $errors->first('meta_description') }}
                                                </div>
                                            @endif
                                            <span class="help-block">{{ trans('cruds.page.fields.meta_description_helper') }}</span>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label class="form-label" for="meta_keywords">{{ trans('cruds.page.fields.meta_keywords') }}</label>
                                            <textarea class="form-control {{ $errors->has('meta_keywords') ? 'is-invalid' : '' }}" name="meta_keywords" id="meta_keywords">{{ old('meta_keywords', $page->meta_keywords) }}</textarea>
                                            @if($errors->has('meta_keywords'))
                                                <div class="invalid-feedback">
                                                    {{ $errors->first('meta_keywords') }}
                                                </div>
                                            @endif
                                            <span class="help-block">{{ trans('cruds.page.fields.meta_keywords_helper') }}</span>
                                        </div>

                                        <input type="hidden" name="section" value="current_page">
                                        
                                        
                                        <div class="form-group mb-3 mb-3">
                                            <button class="btn btn-sm btn-danger" type="submit">
                                                {{ trans('global.save') }}
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
   
@endsection

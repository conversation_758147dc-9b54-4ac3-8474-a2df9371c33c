@extends('layouts.admin')
@section('header_title')
    {{ trans('global.list') }} {{ trans('cruds.page.title_singular') }}
@endsection
@section('styles')
    <link href="{{ url('/') }}/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container"  >
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('global.list') }} {{ trans('cruds.page.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href=""class="text-muted text-hover-primary">
                            {{ trans('cruds.page.title_singular') }}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">
                        {{ trans('global.list') }} {{ trans('cruds.page.title_singular') }}
                    </li>
                </ul>
            </div>

        </div>
    </div>
@endsection

@section('content')
    <div class="card">

        @can('page_create')
            @include('csvImport.modal', [
                'model' => 'page',
                'route' => 'admin.pages.parseCsvImport',
                'model_id' => 'pagesCsvImportModal',
            ])
        @endcan

        <div class="card-body">
            <table class="ajaxTable datatable datatable-page table align-middle table-row-dashed fs-6 gy-5">
                <thead>
                    <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                        <th>
                            {{ trans('cruds.page.fields.id') }}
                        </th>
                        <th>
                            {{ trans('cruds.page.fields.title') }}
                        </th>
                        <th>
                            {{ trans('cruds.page.fields.slug') }}
                        </th>
                        <th>
                            {{ trans('cruds.page.fields.meta_description') }}
                        </th>
                        <th>
                            {{ trans('cruds.page.fields.meta_keywords') }}
                        </th>
                        <th>
                            {{ trans('cruds.page.fields.content') }}
                        </th>
                        <th>
                            ACTIONS
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
@endsection

@section('scripts')
    @parent
    <script>
        $(function() {

            let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)

            let createButtonText = '{{ trans('global.add') }} {{ trans('cruds.page.title_singular') }}';
            let createButtonUrl = "{{ route('admin.pages.create') }}";
            let dtOverrideGlobalsUrl = "{{ route('admin.pages.index') }}";
            let tableName = '.datatable-page';

            

            let dtOverrideGlobals = {
                buttons: dtButtons,
                processing: true,
                serverSide: true,
                retrieve: true,
                aaSorting: [],
                ajax: dtOverrideGlobalsUrl,
                columns: [ {
                        data: 'id',
                        name: 'id'
                    },
                    {
                        data: 'title',
                        name: 'title'
                    },
                    {
                        data: 'slug',
                        name: 'slug'
                    },
                    {
                        data: 'meta_description',
                        name: 'meta_description'
                    },
                    {
                        data: 'meta_keywords',
                        name: 'meta_keywords'
                    },
                    {
                        data: 'content',
                        name: 'content'
                    },
                    {
                        data: 'actions',
                        name: '{{ trans('global.actions') }}',
                        className: 'text-end',
                    }
                ],
                orderCellsTop: true,
                order: [
                    [0, 'desc']
                ],
                pageLength: 10,

                initComplete: function() {
                    $('.dt-buttons').removeClass('btn-group');

                    var searchLabel = $('.dataTables_filter label');
                    searchLabel.addClass('d-flex align-items-center position-relative my-1');

                    var searchInput = searchLabel.find('input');
                    searchInput.addClass('form-control form-control-solid w-250px ps-13 ms-0');
                    searchInput.attr('type', 'text');
                    searchInput.attr('placeholder', 'Search here...');

                    var iconHtml =
                        '<i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5"><span class="path1"></span><span class="path2"></span></i>';
                    searchLabel.prepend(iconHtml);

                    searchLabel.contents().filter(function() {
                        return this.nodeType === 3;
                    }).remove();

                    $('.dataTables_length label').contents().filter(function() {
                        return this.nodeType === 3;
                    }).each(function() {
                        this.textContent = this.textContent.replace('Show', '').replace('entries',
                            '');
                    });
                }
            };
            let table = $(tableName).DataTable(dtOverrideGlobals);
            $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e) {
                $($.fn.dataTable.tables(true)).DataTable()
                    .columns.adjust();
            });

            let visibleColumnsIndexes = null;
            $('.datatable thead').on('input', '.search', function() {
                let strict = $(this).attr('strict') || false
                let value = strict && this.value ? "^" + this.value + "$" : this.value

                let index = $(this).parent().index()
                if (visibleColumnsIndexes !== null) {
                    index = visibleColumnsIndexes[index]
                }

                table
                    .column(index)
                    .search(value, strict)
                    .draw()
            });
            table.on('column-visibility.dt', function(e, settings, column, state) {
                visibleColumnsIndexes = []
                table.columns(":visible").every(function(colIdx) {
                    visibleColumnsIndexes.push(colIdx);
                });
            })
        });
    </script>
@endsection

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Page Editor</title>

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Baskervville:ital@0;1&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
    <link rel="stylesheet" href="{{ asset('css/output.css') }}?v={{ rand(1, 1000000) }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="{{ asset('css/page_editor.css') }}?v={{ rand(1, 1000000) }}">
    <script src="{{ url('/') }}/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js"></script>

</head>

<body class="bg-gray-100 flex">


    <!-- Sidebar (Previously Drawer) -->
    <div class=" bg-white border-r flex flex-col shadow-lg" id="drawer">
        <div class="p-4 border-b flex items-center justify-between">
            <h6 class="text-sm font-semibold text-gray-500 uppercase">
                Modifier Section
            </h6>
            <label for="Toggle3" class="inline-flex items-center rounded-md cursor-pointer dark:text-gray-100 me-3">
                <input id="Toggle3" type="checkbox" class="hidden peer" {{ $language === 'en' ? 'checked' : '' }}>
                <span data-language="fr" class="px-2 py-1 rounded-l-md text-xs {{ $language === 'fr' ? 'dark:bg-violet-600' : 'dark:bg-gray-700' }}">FR</span>
                <span data-language="en" class="px-2 py-1 rounded-r-md text-xs {{ $language === 'en' ? 'dark:bg-violet-600' : 'dark:bg-gray-700' }}">EN</span>
            </label>
        </div>

        <form id="drawer-form" class="p-2 flex-grow overflow-y-auto " action="{{ route('admin.pages.update', $page->id) }}" method="POST">
            @csrf
            @method('PUT')

            <div id="drawer-content" class="no-scrollbar p-1">



            </div>

            <div class="border-t pt-4">
                <div class="flex justify-between space-x-2">
                    <button type="reset" class="px-2 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-100 text-xs">
                        Cancel
                    </button>
                    <button type="submit" class="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs">
                        Update
                    </button>
                </div>
            </div>
        </form>
    </div>
    <!-- Sidebar end -->

    <!-- Main Content -->
    <div class="bg-[url('{{ $bg_url }}')] bg-cover bg-center min-h-screen flex flex-col flex-grow relative" class="" id="page-content">

        @include('frontend.section.header', ['section_header' => $section_header])

        <main  class="flex-1 flex justify-end items-center px-4 sm:px-6 lg:px-24">
            @include('frontend.page.' . $section, ['section_data' => $section_data])
            @include('frontend.section.footer', ['section_footer' => $section_footer])
        </main>

    </div>

    {{-- gallery modal --}}
    <button data-modal-target="extralarge-modal" data-modal-toggle="extralarge-modal" class="open-gallery-button hidden" type="button">Extra large modal</button>

    <div id="extralarge-modal" tabindex="-1" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative w-full max-w-4xl max-h-full">

            <div class="relative bg-white rounded-lg shadow-sm ">

                <div class="flex items-center justify-between p-2 md:p-2 border-b rounded-t  border-gray-200">
                    <h6 class="text-sm font-medium text-gray-900 ">
                        Photo Gallery
                    </h6>
                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="extralarge-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>

                <div class="p-4 md:p-5 space-y-4 overflow-y-auto" id="gallery-modal-body" style="height: 56vh;">

                </div>

                <div class="flex items-center p-4 md:p-5 space-x-3 rtl:space-x-reverse border-t border-gray-200 rounded-b ">

                </div>
            </div>
        </div>
    </div>
    {{-- gallery modal end --}}


    {{-- gallery modal script --}}
    <script>
        $(document).ready(function() {

            // $('.open-gallery-button').on('click', function(e) {
            //     e.preventDefault();
            //     let url = "{{ route('admin.galleries.index', $page->id) }}";
            //     loadGallery(url);
            // });

            $(document).on('submit', '#gallery-modal-body #gallery-search-form', function(e) {
                e.preventDefault();
                let formData = $(this).serialize();
                let url = "{{ route('admin.galleries.index', $page->id) }}?" + formData;
                loadGallery(url);
            });

            $(document).on('click', '#gallery-modal-body .pagination a', function(e) {
                e.preventDefault();
                let url = $(this).attr('href');
                loadGallery(url);
            });

            $(document).on('click', '#search-upload-gallery', function(e) {
                e.preventDefault();
                $('#hidden-upload-input').trigger('click');
            });

            $(document).on('change', '#hidden-upload-input', function(e) {
                let file = this.files[0];
                if (!file) return;

                showLoadingSpinner();

                let formData = new FormData();
                formData.append('file', file);
                formData.append('_token', "{{ csrf_token() }}");

                $.ajax({
                    url: "{{ route('admin.galleries.upload', $page->id) }}",
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        loadGallery("{{ route('admin.galleries.index', $page->id) }}");
                    },
                    error: function(xhr) {
                        $('#gallery-modal-body').html(xhr.responseText);
                    }
                });
            });

            function loadGallery(url) {
                showLoadingSpinner();
                $.ajax({
                    url: url,
                    method: 'GET',
                    dataType: 'html',
                    success: function(response) {
                        $('#gallery-modal-body').html(response);
                    },
                    error: function(xhr) {
                        console.log('Error:', xhr.responseText);
                    }
                });
            }

            function showLoadingSpinner() {
                $("#gallery-modal-body").html(
                    '<div class="flex justify-center items-center h-48">' +
                    '<div class="w-8 h-8 border-4 border-blue-500 border-t-transparent border-solid rounded-full animate-spin"></div>' +
                    '</div>'
                );
            }

            window.copyUrl = function(url, container) {

                if (navigator.clipboard) {
                    navigator.clipboard.writeText(url)
                        .then(() => setCopiedText(container))
                        .catch(err => console.error('Failed to copy: ', err));
                } else {
                    const input = document.createElement('input');
                    input.value = url;
                    document.body.appendChild(input);
                    input.select();
                    document.execCommand('copy');
                    document.body.removeChild(input);
                    setCopiedText(container);
                }
            }

            function setCopiedText(container) {
                const overlay = container.querySelector('[data-copy-text]');
                if (!overlay) return;

                overlay.textContent = 'Copied!';

                function restoreText() {
                    overlay.textContent = 'Click to copy image url';
                    container.removeEventListener('mouseleave', restoreText);
                }

                container.addEventListener('mouseleave', restoreText);
            }

            window.selectedGalleryInput = null;

            $(document).on('click', '[data-gallery]', function(e) {
                e.preventDefault();

                window.selectedGalleryInput = $(this);

                $('.open-gallery-button').trigger('click');
                let url = "{{ route('admin.galleries.index', $page->id) }}";
                loadGallery(url);

            });

            $(document).on('click', '#gallery-modal-body .border.rounded[data-image-url]', function(e) {
                e.preventDefault();
                let imageUrl = $(this).attr('data-image-url');

                if (window.selectedGalleryInput) {
                    window.selectedGalleryInput.val(imageUrl);
                }

                $('[data-modal-hide="extralarge-modal"]').trigger('click');
            });


        });
    </script>
    {{-- gallery modal script end --}}

    {{-- drawer form script --}}
    <script>
        $(function() {

            window.onload = function() {
                const section = @json($section ?? '');
                if (section) {
                    const target = document.querySelector(`[data-section="${section}"]`);
                    if (target) {
                        target.scrollIntoView({
                            behavior: "smooth",
                            block: "start"
                        });

                    }
                }
                drawerForm('{{ $section }}', '{{ $language }}');
                $("#drawerButton").trigger("click");
            };

            $("[data-section]").on("click", function(e) {
                e.preventDefault();
                let sectionValue = $(this).attr("data-section");
                $("#drawer-content").html(
                    '<div class="flex justify-center items-center h-48">' +
                    '<div class="w-8 h-8 border-4 border-blue-500 border-t-transparent border-solid rounded-full animate-spin"></div>' +
                    '</div>'
                );
                drawerForm(sectionValue, '{{ $language }}');
                $("#drawerButton").trigger("click");
            });

            $(document).on("click", "[data-drawer-dismiss]", function() {
                $("#drawer-content").html("");
            });

            function drawerForm(section, language) {
                $.ajax({
                    url: "{{ route('admin.pages.drawerForm') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        section: section,
                        page_id: "{{ $page->id }}",
                        language: language
                    },
                    success: function(data) {
                        $("#drawer-content").html(data.content);
                        initDrawerForm(data.section_name);
                    }
                });
            }

            function initDrawerForm(sectionName) {
                if (sectionName === "header") {
                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="URL" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                }

                if (sectionName === "index") {
                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Text" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });
                    $("#add-menu-item-1").on("click", function() {
                        $("#menu-list-1").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item-1 ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item-1", function() {
                        $(this).closest("li").remove();
                    });

                    $("#add-menu-item-2").on("click", function() {
                        $("#menu-list-2").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="Url" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item-2 ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-menu-item-2", function() {
                        $(this).closest("li").remove();
                    });

                }

                if (sectionName === "carousel") {
                    $("#add-carousel-item").on("click", function() {
                        $("#carousel-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input type="text" data-gallery="carousel_image" name="carousel_image" placeholder="Image URL" class="border rounded p-1 text-xs w-full" />' +
                            '<button type="button" class="delete-carousel-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });
                    $(document).on("click", ".delete-carousel-item", function() {
                        $(this).closest("li").remove();
                    });

                    $("#carousel-list").sortable();

                }

                if (sectionName === "apropos") {

                    ClassicEditor
                        .create(document.querySelector('#apropos_description_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        })
                        .catch(error => {
                            console.error(error);
                        });


                }

                if (sectionName === "casting") {

                    ClassicEditor
                        .create(document.querySelector('#casting_description_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }

                if (sectionName === "jigging") {

                    ClassicEditor
                        .create(document.querySelector('#jigging_description_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }

                if (sectionName === "traine") {

                    ClassicEditor
                        .create(document.querySelector('#traine_description_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }

                if (sectionName === "excursions") {

                    ClassicEditor
                        .create(document.querySelector('#excursions_description_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }

                if (sectionName === "skipper") {

                    ClassicEditor
                        .create(document.querySelector('#skipper_description_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }

                if (sectionName === "securite") {

                    $("#add-securite-item").on("click", function() {
                        $("#securite-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded">' +
                            '<div class="flex items-center mb-2">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="securite_header" type="text" placeholder="Header" class="border rounded p-1 text-xs w-full" />' +
                            '<button type="button" class="delete-securite-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</div>' +
                            '<input name="securite_content" type="text" placeholder="Content" class="border rounded p-1 text-xs w-full" />' +
                            '</li>'
                        );
                    });

                    $(document).on("click", ".delete-securite-item", function() {
                        $(this).closest("li").remove();
                    });

                    $("#add-equipment-item").on("click", function() {
                        $("#equipment-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="equipment_title" type="text" placeholder="Title" class="border rounded p-1 text-xs w-full" />' +
                            '<button type="button" class="delete-equipment-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });

                    $(document).on("click", ".delete-equipment-item", function() {
                        $(this).closest("li").remove();
                    });

                    $("#equipment-list, #securite-list").sortable();

                }

                if (sectionName === "galerie") {

                    $("#add-galerie-item").on("click", function() {
                        $("#galerie-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input type="text" data-gallery="galerie_image" name="galerie_image" placeholder="Image URL" class="border rounded p-1 text-xs w-full" />' +
                            '<button type="button" class="delete-galerie-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });

                    $(document).on("click", ".delete-galerie-item", function() {
                        $(this).closest("li").remove();
                    });

                    $("#galerie-list").sortable();

                }

                if (sectionName === "footer") {

                    $("#add-menu-item").on("click", function() {
                        $("#menu-list").append(
                            '<li class="mb-2 bg-gray-50 p-2 rounded flex items-center">' +
                            '<span type="button" class="me-1 cursor-pointer">=</span>' +
                            '<input name="menu_title" type="text" placeholder="Title" class="mr-2 border rounded p-1 text-xs w-1/3" />' +
                            '<input name="menu_url" type="text" placeholder="URL" class="border rounded p-1 text-xs w-2/3" />' +
                            '<button type="button" class="delete-menu-item ml-2">' +
                            '<svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">' +
                            '<path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>' +
                            '</svg>' +
                            '</button>' +
                            '</li>'
                        );
                    });

                    $(document).on("click", ".delete-menu-item", function() {
                        $(this).closest("li").remove();
                    });

                    $("#menu-list").sortable();

                    ClassicEditor
                        .create(document.querySelector('#address_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        })
                        .catch(error => {
                            console.error(error);
                        });

                    ClassicEditor
                        .create(document.querySelector('#copyright_data'))
                        .then(editor => {
                            const editableElement = editor.ui.view.editable.element;
                            editableElement.style.height = '375px';
                            editableElement.style.maxHeight = '375px';
                            editableElement.style.overflowY = 'auto';
                        });
                }



            }

            $("#drawer-form").on("submit", function() {
                var currentSection = $("#drawer-content").find('input[name="section"]').val();
                if (currentSection === "header") {
                    var menuData = [];
                    $("#menu-list li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#menu_data").val(JSON.stringify(menuData));

                    var socialData = [];
                    $("#social-list li").each(function() {
                        var url = $(this).find('input[name="social_url"]').val();
                        var img = $(this).find('input[name="social_image"]').val();
                        socialData.push({
                            url: url,
                            image: img
                        });
                    });
                    $("#social_data").val(JSON.stringify(socialData));

                }

                if (currentSection === "index") {
                    var menuData = [];
                    $("#menu-list li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_1_data").val(JSON.stringify(menuData));

                    var menuData_1 = [];
                    $("#menu-list-1 li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData_1.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_2_data").val(JSON.stringify(menuData_1));

                    var menuData_2 = [];
                    $("#menu-list-2 li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData_2.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#list_3_data").val(JSON.stringify(menuData_2));

                }

                if (currentSection === "carousel") {
                    var carouselData = [];
                    $("#carousel-list li").each(function() {
                        var img = $(this).find('input[name="carousel_image"]').val();
                        carouselData.push({
                            image: img
                        });
                    });
                    $("#carousel_data").val(JSON.stringify(carouselData));
                }

                if (currentSection === "securite") {
                    var securiteData = [];
                    $("#securite-list li").each(function() {
                        var header = $(this).find('input[name="securite_header"]').val();
                        var content = $(this).find('input[name="securite_content"]').val();
                        securiteData.push({
                            header: header,
                            content: content
                        });
                    });
                    $("#securite_data").val(JSON.stringify(securiteData));

                    var equipmentData = [];
                    $("#equipment-list li").each(function() {
                        var title = $(this).find('input[name="equipment_title"]').val();
                        equipmentData.push({
                            title: title
                        });
                    });
                    $("#equipment_data").val(JSON.stringify(equipmentData));

                }

                if (currentSection === "galerie") {
                    var galerieData = [];
                    $("#galerie-list li").each(function() {
                        var img = $(this).find('input[name="galerie_image"]').val();
                        galerieData.push({
                            image: img
                        });
                    });
                    $("#galerie_data").val(JSON.stringify(galerieData));
                }

                if (currentSection === "footer") {
                    var menuData = [];
                    $("#menu-list li").each(function() {
                        var title = $(this).find('input[name="menu_title"]').val();
                        var url = $(this).find('input[name="menu_url"]').val();
                        menuData.push({
                            title: title,
                            url: url
                        });
                    });
                    $("#menu_data").val(JSON.stringify(menuData));

                }

            });

        });
    </script>
    {{-- drawer form script end --}}

    {{-- language and section --}}
    <script>
        $(document).ready(function() {
            $('[data-language]').on('click', function() {
                var language = $(this).data('language');
                var pageId = @json($page->id ?? 1);
                var currentSection = $("#drawer-content").find('input[name="section"]').val();
                window.location.href = `/admin/pages/${pageId}/${language}/${currentSection}`;
            });
        });
    </script>
    {{-- language and section end --}}

</body>

</html>

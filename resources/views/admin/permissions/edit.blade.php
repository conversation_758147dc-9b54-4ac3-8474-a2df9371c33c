@extends('layouts.admin')

@section('header_title')
{{ trans('global.edit') }} {{ trans('cruds.permission.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.permission.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="index.html" class="text-muted text-hover-primary">{{ trans('cruds.permission.title_singular') }}</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">{{ trans('global.edit') }}
                        {{ trans('cruds.permission.title_singular') }}</li>
                </ul>
            </div>

        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">

        <div id="kt_app_content" class="app-content flex-column-fluid">

            <div id="kt_app_content_container" class="container-xxxl">

                <div class="row g-0">
                    <div class="col-lg-6 col-6 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4> {{ trans('global.edit') }} {{ trans('cruds.permission.title_singular') }}</h4>
                            </div>
                            <div class="card-body p-0">
                                <form method="POST" action="{{ route("admin.permissions.update", [$permission->id]) }}" enctype="multipart/form-data">
                                    @method('PUT')
                                    @csrf
                                    <div class="form-group mb-3">
                                        <label class="required form-label" for="title">{{ trans('cruds.permission.fields.title') }}</label>
                                        <input class="form-control {{ $errors->has('title') ? 'is-invalid' : '' }}" type="text" name="title" id="title" value="{{ old('title', $permission->title) }}" required>
                                        @if($errors->has('title'))
                                            <div class="invalid-feedback">
                                                {{ $errors->first('title') }}
                                            </div>
                                        @endif
                                        <span class="help-block">{{ trans('cruds.permission.fields.title_helper') }}</span>
                                    </div>
                                    <div class="form-group mb-3">
                                        <button class="btn btn-danger btn-sm" type="submit">
                                            {{ trans('global.save') }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>

    </div>
@endsection

@section('scripts')
@endsection

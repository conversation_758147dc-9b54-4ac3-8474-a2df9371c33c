@extends('layouts.admin')

@section('header_title')
{{ trans('global.list') }} {{ trans('cruds.permission.title_singular') }}
@endsection

@section('styles')
    <link href="{{ url('/') }}/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container" class="">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('global.list') }} {{ trans('cruds.permission.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href=""class="text-muted text-hover-primary">
                            {{ trans('cruds.permission.title_singular') }}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">
                        {{ trans('global.list') }} {{ trans('cruds.permission.title_singular') }}
                    </li>
                </ul>
            </div>

        </div>
    </div>
@endsection

@section('content')
    <div class="card">

        @can('permission_create')
            @include('csvImport.modal', [
                'model' => 'Permission',
                'route' => 'admin.permissions.parseCsvImport',
                'model_id' => 'permissionsCsvImportModal',
            ])
        @endcan

        <div class="card-body">
            <table class="ajaxTable datatable datatable-Permission table align-middle table-row-dashed fs-6 gy-5">
                <thead>
                    <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                        <th>
                            {{ trans('cruds.permission.fields.id') }}
                        </th>
                        <th>
                            {{ trans('cruds.permission.fields.title') }}
                        </th>
                        <th>
                            ACTIONS
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
@endsection

@section('scripts')
    @parent
    <script>
        $(function() {

            let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)

            let createButtonText = '{{ trans('global.add') }} {{ trans('cruds.permission.title_singular') }}';
            let createButtonUrl = "{{ route('admin.permissions.create') }}";
            let dtOverrideGlobalsUrl = "{{ route('admin.permissions.index') }}";
            let tableName = '.datatable-Permission';

            @can('permission_create')

                let csvImportButton = {
                    text: '{{ trans('global.app_csvImport') }}',
                    className: 'btn btn-light-info btn-sm hover-scale',
                    action: function(e, dt, node, config) {
                        $('#permissionsCsvImportModal').modal('show');
                    }
                };
                dtButtons.push(csvImportButton);

                let createButton = {
                    text: createButtonText,
                    className: 'btn-sm btn-light-primary hover-scale',
                    action: function(e, dt, node, config) {
                        window.location.href = createButtonUrl;
                    }
                };
                dtButtons.push(createButton);
            @endcan

            let dtOverrideGlobals = {
                buttons: dtButtons,
                processing: true,
                serverSide: true,
                retrieve: true,
                aaSorting: [],
                ajax: dtOverrideGlobalsUrl,
                columns: [{
                        data: 'id',
                        name: 'id'
                    },
                    {
                        data: 'title',
                        name: 'title'
                    },
                    {
                        data: 'actions',
                        name: '{{ trans('global.actions') }}',
                        className: 'text-end',
                    }
                ],
                orderCellsTop: true,
                order: [
                    [0, 'desc']
                ],
                pageLength: 10,

                initComplete: function() {
                    $('.dt-buttons').removeClass('btn-group');

                    var searchLabel = $('.dataTables_filter label');
                    searchLabel.addClass('d-flex align-items-center position-relative my-1');

                    var searchInput = searchLabel.find('input');
                    searchInput.addClass('form-control form-control-solid w-250px ps-13 ms-0');
                    searchInput.attr('type', 'text');
                    searchInput.attr('placeholder', 'Search here...');

                    var iconHtml =
                        '<i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5"><span class="path1"></span><span class="path2"></span></i>';
                    searchLabel.prepend(iconHtml);

                    searchLabel.contents().filter(function() {
                        return this.nodeType === 3;
                    }).remove();

                    $('.dataTables_length label').contents().filter(function() {
                        return this.nodeType === 3;
                    }).each(function() {
                        this.textContent = this.textContent.replace('Show', '').replace('entries',
                            '');
                    });
                }
            };
            let table = $(tableName).DataTable(dtOverrideGlobals);
            $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e) {
                $($.fn.dataTable.tables(true)).DataTable()
                    .columns.adjust();
            });

            let visibleColumnsIndexes = null;
            $('.datatable thead').on('input', '.search', function() {
                let strict = $(this).attr('strict') || false
                let value = strict && this.value ? "^" + this.value + "$" : this.value

                let index = $(this).parent().index()
                if (visibleColumnsIndexes !== null) {
                    index = visibleColumnsIndexes[index]
                }

                table
                    .column(index)
                    .search(value, strict)
                    .draw()
            });
            table.on('column-visibility.dt', function(e, settings, column, state) {
                visibleColumnsIndexes = []
                table.columns(":visible").every(function(colIdx) {
                    visibleColumnsIndexes.push(colIdx);
                });
            })
        });
    </script>
@endsection

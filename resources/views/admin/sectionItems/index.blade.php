@extends('layouts.admin')
@section('content')
@can('section_item_create')
    <div style="margin-bottom: 10px;" class="row">
        <div class="col-lg-12">
            <a class="btn btn-success" href="{{ route('admin.section-items.create') }}">
                {{ trans('global.add') }} {{ trans('cruds.sectionItem.title_singular') }}
            </a>
            <button class="btn btn-warning" data-toggle="modal" data-target="#csvImportModal">
                {{ trans('global.app_csvImport') }}
            </button>
            @include('csvImport.modal', ['model' => 'SectionItem', 'route' => 'admin.section-items.parseCsvImport'])
        </div>
    </div>
@endcan
<div class="card">
    <div class="card-header">
        {{ trans('cruds.sectionItem.title_singular') }} {{ trans('global.list') }}
    </div>

    <div class="card-body">
        <table class=" table table-bordered table-striped table-hover ajaxTable datatable datatable-SectionItem">
            <thead>
                <tr>
                    <th width="10">

                    </th>
                    <th>
                        {{ trans('cruds.sectionItem.fields.id') }}
                    </th>
                    <th>
                        {{ trans('cruds.sectionItem.fields.section') }}
                    </th>
                    <th>
                        {{ trans('cruds.section.fields.title') }}
                    </th>
                    <th>
                        {{ trans('cruds.sectionItem.fields.title') }}
                    </th>
                    <th>
                        {{ trans('cruds.sectionItem.fields.url') }}
                    </th>
                    <th>
                        {{ trans('cruds.sectionItem.fields.image') }}
                    </th>
                    <th>
                        {{ trans('cruds.sectionItem.fields.content') }}
                    </th>
                    <th>
                        {{ trans('cruds.sectionItem.fields.sort_order') }}
                    </th>
                    <th>
                        &nbsp;
                    </th>
                </tr>
            </thead>
        </table>
    </div>
</div>



@endsection
@section('scripts')
@parent
<script>
    $(function () {
  let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)
@can('section_item_delete')
  let deleteButtonTrans = '{{ trans('global.datatables.delete') }}';
  let deleteButton = {
    text: deleteButtonTrans,
    url: "{{ route('admin.section-items.massDestroy') }}",
    className: 'btn-danger',
    action: function (e, dt, node, config) {
      var ids = $.map(dt.rows({ selected: true }).data(), function (entry) {
          return entry.id
      });

      if (ids.length === 0) {
        alert('{{ trans('global.datatables.zero_selected') }}')

        return
      }

      if (confirm('{{ trans('global.areYouSure') }}')) {
        $.ajax({
          headers: {'x-csrf-token': _token},
          method: 'POST',
          url: config.url,
          data: { ids: ids, _method: 'DELETE' }})
          .done(function () { location.reload() })
      }
    }
  }
  dtButtons.push(deleteButton)
@endcan

  let dtOverrideGlobals = {
    buttons: dtButtons,
    processing: true,
    serverSide: true,
    retrieve: true,
    aaSorting: [],
    ajax: "{{ route('admin.section-items.index') }}",
    columns: [
      { data: 'placeholder', name: 'placeholder' },
{ data: 'id', name: 'id' },
{ data: 'section_section_type', name: 'section.section_type' },
{ data: 'section.title', name: 'section.title' },
{ data: 'title', name: 'title' },
{ data: 'url', name: 'url' },
{ data: 'image', name: 'image', sortable: false, searchable: false },
{ data: 'content', name: 'content' },
{ data: 'sort_order', name: 'sort_order' },
{ data: 'actions', name: '{{ trans('global.actions') }}' }
    ],
    orderCellsTop: true,
    order: [[ 1, 'desc' ]],
    pageLength: 25,
  };
  let table = $('.datatable-SectionItem').DataTable(dtOverrideGlobals);
  $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e){
      $($.fn.dataTable.tables(true)).DataTable()
          .columns.adjust();
  });
  
});

</script>
@endsection
@extends('layouts.admin')
@section('content')

<div class="card">
    <div class="card-header">
        {{ trans('global.show') }} {{ trans('cruds.sectionItem.title') }}
    </div>

    <div class="card-body">
        <div class="form-group">
            <div class="form-group">
                <a class="btn btn-default" href="{{ route('admin.section-items.index') }}">
                    {{ trans('global.back_to_list') }}
                </a>
            </div>
            <table class="table table-bordered table-striped">
                <tbody>
                    <tr>
                        <th>
                            {{ trans('cruds.sectionItem.fields.id') }}
                        </th>
                        <td>
                            {{ $sectionItem->id }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.sectionItem.fields.section') }}
                        </th>
                        <td>
                            {{ $sectionItem->section->section_type ?? '' }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.sectionItem.fields.title') }}
                        </th>
                        <td>
                            {{ $sectionItem->title }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.sectionItem.fields.url') }}
                        </th>
                        <td>
                            {{ $sectionItem->url }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.sectionItem.fields.image') }}
                        </th>
                        <td>
                            @if($sectionItem->image)
                                <a href="{{ $sectionItem->image->getUrl() }}" target="_blank" style="display: inline-block">
                                    <img src="{{ $sectionItem->image->getUrl('thumb') }}">
                                </a>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.sectionItem.fields.content') }}
                        </th>
                        <td>
                            {{ $sectionItem->content }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.sectionItem.fields.sort_order') }}
                        </th>
                        <td>
                            {{ $sectionItem->sort_order }}
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="form-group">
                <a class="btn btn-default" href="{{ route('admin.section-items.index') }}">
                    {{ trans('global.back_to_list') }}
                </a>
            </div>
        </div>
    </div>
</div>



@endsection
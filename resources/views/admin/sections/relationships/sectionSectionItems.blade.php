@can('section_item_create')
    <div style="margin-bottom: 10px;" class="row">
        <div class="col-lg-12">
            <a class="btn btn-success" href="{{ route('admin.section-items.create') }}">
                {{ trans('global.add') }} {{ trans('cruds.sectionItem.title_singular') }}
            </a>
        </div>
    </div>
@endcan

<div class="card">
    <div class="card-header">
        {{ trans('cruds.sectionItem.title_singular') }} {{ trans('global.list') }}
    </div>

    <div class="card-body">
        <div class="table-responsive">
            <table class=" table table-bordered table-striped table-hover datatable datatable-sectionSectionItems">
                <thead>
                    <tr>
                        <th width="10">

                        </th>
                        <th>
                            {{ trans('cruds.sectionItem.fields.id') }}
                        </th>
                        <th>
                            {{ trans('cruds.sectionItem.fields.section') }}
                        </th>
                        <th>
                            {{ trans('cruds.section.fields.title') }}
                        </th>
                        <th>
                            {{ trans('cruds.sectionItem.fields.title') }}
                        </th>
                        <th>
                            {{ trans('cruds.sectionItem.fields.url') }}
                        </th>
                        <th>
                            {{ trans('cruds.sectionItem.fields.image') }}
                        </th>
                        <th>
                            {{ trans('cruds.sectionItem.fields.content') }}
                        </th>
                        <th>
                            {{ trans('cruds.sectionItem.fields.sort_order') }}
                        </th>
                        <th>
                            &nbsp;
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($sectionItems as $key => $sectionItem)
                        <tr data-entry-id="{{ $sectionItem->id }}">
                            <td>

                            </td>
                            <td>
                                {{ $sectionItem->id ?? '' }}
                            </td>
                            <td>
                                {{ $sectionItem->section->section_type ?? '' }}
                            </td>
                            <td>
                                {{ $sectionItem->section->title ?? '' }}
                            </td>
                            <td>
                                {{ $sectionItem->title ?? '' }}
                            </td>
                            <td>
                                {{ $sectionItem->url ?? '' }}
                            </td>
                            <td>
                                @if($sectionItem->image)
                                    <a href="{{ $sectionItem->image->getUrl() }}" target="_blank" style="display: inline-block">
                                        <img src="{{ $sectionItem->image->getUrl('thumb') }}">
                                    </a>
                                @endif
                            </td>
                            <td>
                                {{ $sectionItem->content ?? '' }}
                            </td>
                            <td>
                                {{ $sectionItem->sort_order ?? '' }}
                            </td>
                            <td>
                                @can('section_item_show')
                                    <a class="btn btn-xs btn-primary" href="{{ route('admin.section-items.show', $sectionItem->id) }}">
                                        {{ trans('global.view') }}
                                    </a>
                                @endcan

                                @can('section_item_edit')
                                    <a class="btn btn-xs btn-info" href="{{ route('admin.section-items.edit', $sectionItem->id) }}">
                                        {{ trans('global.edit') }}
                                    </a>
                                @endcan

                                @can('section_item_delete')
                                    <form action="{{ route('admin.section-items.destroy', $sectionItem->id) }}" method="POST" onsubmit="return confirm('{{ trans('global.areYouSure') }}');" style="display: inline-block;">
                                        <input type="hidden" name="_method" value="DELETE">
                                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                        <input type="submit" class="btn btn-xs btn-danger" value="{{ trans('global.delete') }}">
                                    </form>
                                @endcan

                            </td>

                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

@section('scripts')
@parent
<script>
    $(function () {
  let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)
@can('section_item_delete')
  let deleteButtonTrans = '{{ trans('global.datatables.delete') }}'
  let deleteButton = {
    text: deleteButtonTrans,
    url: "{{ route('admin.section-items.massDestroy') }}",
    className: 'btn-danger',
    action: function (e, dt, node, config) {
      var ids = $.map(dt.rows({ selected: true }).nodes(), function (entry) {
          return $(entry).data('entry-id')
      });

      if (ids.length === 0) {
        alert('{{ trans('global.datatables.zero_selected') }}')

        return
      }

      if (confirm('{{ trans('global.areYouSure') }}')) {
        $.ajax({
          headers: {'x-csrf-token': _token},
          method: 'POST',
          url: config.url,
          data: { ids: ids, _method: 'DELETE' }})
          .done(function () { location.reload() })
      }
    }
  }
  dtButtons.push(deleteButton)
@endcan

  $.extend(true, $.fn.dataTable.defaults, {
    orderCellsTop: true,
    order: [[ 1, 'desc' ]],
    pageLength: 25,
  });
  let table = $('.datatable-sectionSectionItems:not(.ajaxTable)').DataTable({ buttons: dtButtons })
  $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e){
      $($.fn.dataTable.tables(true)).DataTable()
          .columns.adjust();
  });
  
})

</script>
@endsection
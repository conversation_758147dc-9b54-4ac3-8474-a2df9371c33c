@extends('layouts.admin')
@section('content')

<div class="card">
    <div class="card-header">
        {{ trans('global.show') }} {{ trans('cruds.section.title') }}
    </div>

    <div class="card-body">
        <div class="form-group">
            <div class="form-group">
                <a class="btn btn-default" href="{{ route('admin.sections.index') }}">
                    {{ trans('global.back_to_list') }}
                </a>
            </div>
            <table class="table table-bordered table-striped">
                <tbody>
                    <tr>
                        <th>
                            {{ trans('cruds.section.fields.id') }}
                        </th>
                        <td>
                            {{ $section->id }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.section.fields.page') }}
                        </th>
                        <td>
                            {{ $section->page->title ?? '' }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.section.fields.section_type') }}
                        </th>
                        <td>
                            {{ App\Models\Section::SECTION_TYPE_SELECT[$section->section_type] ?? '' }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.section.fields.title') }}
                        </th>
                        <td>
                            {{ $section->title }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.section.fields.text') }}
                        </th>
                        <td>
                            {{ $section->text }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.section.fields.image') }}
                        </th>
                        <td>
                            @if($section->image)
                                <a href="{{ $section->image->getUrl() }}" target="_blank" style="display: inline-block">
                                    <img src="{{ $section->image->getUrl('thumb') }}">
                                </a>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.section.fields.google_url') }}
                        </th>
                        <td>
                            {{ $section->google_url }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.section.fields.sort_order') }}
                        </th>
                        <td>
                            {{ $section->sort_order }}
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="form-group">
                <a class="btn btn-default" href="{{ route('admin.sections.index') }}">
                    {{ trans('global.back_to_list') }}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        {{ trans('global.relatedData') }}
    </div>
    <ul class="nav nav-tabs" role="tablist" id="relationship-tabs">
        <li class="nav-item">
            <a class="nav-link" href="#section_section_items" role="tab" data-toggle="tab">
                {{ trans('cruds.sectionItem.title') }}
            </a>
        </li>
    </ul>
    <div class="tab-content">
        <div class="tab-pane" role="tabpanel" id="section_section_items">
            @includeIf('admin.sections.relationships.sectionSectionItems', ['sectionItems' => $section->sectionSectionItems])
        </div>
    </div>
</div>

@endsection
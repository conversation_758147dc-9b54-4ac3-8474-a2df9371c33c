@extends('layouts.admin')
@section('content')
    <div class="card">
        <div class="card-header">
            {{ trans('global.create') }} {{ trans('cruds.seo.title_singular') }}
        </div>

        <div class="card-body">
            <form method="POST" action="{{ route('admin.seos.store') }}" enctype="multipart/form-data">
                @csrf
                <div class="form-group">
                    <label for="meta_title">{{ trans('cruds.seo.fields.meta_title') }}</label>
                    <input class="form-control {{ $errors->has('meta_title') ? 'is-invalid' : '' }}" type="text" name="meta_title" id="meta_title" value="{{ old('meta_title', '') }}">
                    @if ($errors->has('meta_title'))
                        <div class="invalid-feedback">
                            {{ $errors->first('meta_title') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.meta_title_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="meta_description">{{ trans('cruds.seo.fields.meta_description') }}</label>
                    <textarea class="form-control {{ $errors->has('meta_description') ? 'is-invalid' : '' }}" name="meta_description" id="meta_description">{{ old('meta_description') }}</textarea>
                    @if ($errors->has('meta_description'))
                        <div class="invalid-feedback">
                            {{ $errors->first('meta_description') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.meta_description_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="meta_keywords">{{ trans('cruds.seo.fields.meta_keywords') }}</label>
                    <input class="form-control {{ $errors->has('meta_keywords') ? 'is-invalid' : '' }}" type="text" name="meta_keywords" id="meta_keywords" value="{{ old('meta_keywords', '') }}">
                    @if ($errors->has('meta_keywords'))
                        <div class="invalid-feedback">
                            {{ $errors->first('meta_keywords') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.meta_keywords_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="canonical_url">{{ trans('cruds.seo.fields.canonical_url') }}</label>
                    <input class="form-control {{ $errors->has('canonical_url') ? 'is-invalid' : '' }}" type="text" name="canonical_url" id="canonical_url" value="{{ old('canonical_url', '') }}">
                    @if ($errors->has('canonical_url'))
                        <div class="invalid-feedback">
                            {{ $errors->first('canonical_url') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.canonical_url_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="og_image">{{ trans('cruds.seo.fields.og_image') }}</label>
                    <div class="needsclick dropzone {{ $errors->has('og_image') ? 'is-invalid' : '' }}" id="og_image-dropzone">
                    </div>
                    @if ($errors->has('og_image'))
                        <div class="invalid-feedback">
                            {{ $errors->first('og_image') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.og_image_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="og_type">{{ trans('cruds.seo.fields.og_type') }}</label>
                    <input class="form-control {{ $errors->has('og_type') ? 'is-invalid' : '' }}" type="text" name="og_type" id="og_type" value="{{ old('og_type', 'website') }}">
                    @if ($errors->has('og_type'))
                        <div class="invalid-feedback">
                            {{ $errors->first('og_type') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.og_type_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="schema_type">{{ trans('cruds.seo.fields.schema_type') }}</label>
                    <input class="form-control {{ $errors->has('schema_type') ? 'is-invalid' : '' }}" type="text" name="schema_type" id="schema_type" value="{{ old('schema_type', '') }}">
                    @if ($errors->has('schema_type'))
                        <div class="invalid-feedback">
                            {{ $errors->first('schema_type') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.schema_type_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="schema_json">{{ trans('cruds.seo.fields.schema_json') }}</label>
                    <input class="form-control {{ $errors->has('schema_json') ? 'is-invalid' : '' }}" type="text" name="schema_json" id="schema_json" value="{{ old('schema_json', '') }}">
                    @if ($errors->has('schema_json'))
                        <div class="invalid-feedback">
                            {{ $errors->first('schema_json') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.schema_json_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="sitemap_priority">{{ trans('cruds.seo.fields.sitemap_priority') }}</label>
                    <input class="form-control {{ $errors->has('sitemap_priority') ? 'is-invalid' : '' }}" type="text" name="sitemap_priority" id="sitemap_priority" value="{{ old('sitemap_priority', '0.5') }}">
                    @if ($errors->has('sitemap_priority'))
                        <div class="invalid-feedback">
                            {{ $errors->first('sitemap_priority') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.sitemap_priority_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="sitemap_frequency">{{ trans('cruds.seo.fields.sitemap_frequency') }}</label>
                    <input class="form-control {{ $errors->has('sitemap_frequency') ? 'is-invalid' : '' }}" type="text" name="sitemap_frequency" id="sitemap_frequency" value="{{ old('sitemap_frequency', 'monthly') }}">
                    @if ($errors->has('sitemap_frequency'))
                        <div class="invalid-feedback">
                            {{ $errors->first('sitemap_frequency') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.sitemap_frequency_helper') }}</span>
                </div>
                <div class="form-group">
                    <div class="form-check {{ $errors->has('index') ? 'is-invalid' : '' }}">
                        <input type="hidden" name="index" value="0">
                        <input class="form-check-input" type="checkbox" name="index" id="index" value="1" {{ old('index', 0) == 1 || old('index') === null ? 'checked' : '' }}>
                        <label class="form-check-label" for="index">{{ trans('cruds.seo.fields.index') }}</label>
                    </div>
                    @if ($errors->has('index'))
                        <div class="invalid-feedback">
                            {{ $errors->first('index') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.index_helper') }}</span>
                </div>
                <div class="form-group">
                    <label for="page_id">{{ trans('cruds.seo.fields.page') }}</label>
                    <select class="form-control select2 {{ $errors->has('page') ? 'is-invalid' : '' }}" name="page_id" id="page_id">
                        @foreach ($pages as $id => $entry)
                            <option value="{{ $id }}" {{ old('page_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
                        @endforeach
                    </select>
                    @if ($errors->has('page'))
                        <div class="invalid-feedback">
                            {{ $errors->first('page') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.page_helper') }}</span>
                </div>
                <div class="form-group">
                    <div class="form-check {{ $errors->has('follow') ? 'is-invalid' : '' }}">
                        <input type="hidden" name="follow" value="0">
                        <input class="form-check-input" type="checkbox" name="follow" id="follow" value="1" {{ old('follow', 0) == 1 || old('follow') === null ? 'checked' : '' }}>
                        <label class="form-check-label" for="follow">{{ trans('cruds.seo.fields.follow') }}</label>
                    </div>
                    @if ($errors->has('follow'))
                        <div class="invalid-feedback">
                            {{ $errors->first('follow') }}
                        </div>
                    @endif
                    <span class="help-block">{{ trans('cruds.seo.fields.follow_helper') }}</span>
                </div>
                <div class="form-group">
                    <button class="btn btn-danger" type="submit">
                        {{ trans('global.save') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        Dropzone.options.ogImageDropzone = {
            url: '{{ route('admin.seos.storeMedia') }}',
            maxFilesize: 20, // MB
            acceptedFiles: '.jpeg,.jpg,.png,.gif',
            maxFiles: 1,
            addRemoveLinks: true,
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            },
            params: {
                size: 20,
                width: 4096,
                height: 4096
            },
            success: function(file, response) {
                $('form').find('input[name="og_image"]').remove()
                $('form').append('<input type="hidden" name="og_image" value="' + response.name + '">')
            },
            removedfile: function(file) {
                file.previewElement.remove()
                if (file.status !== 'error') {
                    $('form').find('input[name="og_image"]').remove()
                    this.options.maxFiles = this.options.maxFiles + 1
                }
            },
            init: function() {
                @if (isset($seo) && $seo->og_image)
                    var file = {!! json_encode($seo->og_image) !!}
                    this.options.addedfile.call(this, file)
                    this.options.thumbnail.call(this, file, file.preview ?? file.preview_url)
                    file.previewElement.classList.add('dz-complete')
                    $('form').append('<input type="hidden" name="og_image" value="' + file.file_name + '">')
                    this.options.maxFiles = this.options.maxFiles - 1
                @endif
            },
            error: function(file, response) {
                if ($.type(response) === 'string') {
                    var message = response //dropzone sends it's own error messages in string
                } else {
                    var message = response.errors.file
                }
                file.previewElement.classList.add('dz-error')
                _ref = file.previewElement.querySelectorAll('[data-dz-errormessage]')
                _results = []
                for (_i = 0, _len = _ref.length; _i < _len; _i++) {
                    node = _ref[_i]
                    _results.push(node.textContent = message)
                }

                return _results
            }
        }
    </script>
@endsection

@extends('layouts.admin')
@section('content')
@can('seo_create')
    <div style="margin-bottom: 10px;" class="row">
        <div class="col-lg-12">
            <a class="btn btn-success" href="{{ route('admin.seos.create') }}">
                {{ trans('global.add') }} {{ trans('cruds.seo.title_singular') }}
            </a>
            <button class="btn btn-warning" data-toggle="modal" data-target="#csvImportModal">
                {{ trans('global.app_csvImport') }}
            </button>
            @include('csvImport.modal', ['model' => 'Seo', 'route' => 'admin.seos.parseCsvImport'])
        </div>
    </div>
@endcan
<div class="card">
    <div class="card-header">
        {{ trans('cruds.seo.title_singular') }} {{ trans('global.list') }}
    </div>

    <div class="card-body">
        <table class=" table table-bordered table-striped table-hover ajaxTable datatable datatable-Seo">
            <thead>
                <tr>
                    <th width="10">

                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.id') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.meta_title') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.meta_description') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.meta_keywords') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.canonical_url') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.og_image') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.og_type') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.schema_type') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.schema_json') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.sitemap_priority') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.sitemap_frequency') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.index') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.page') }}
                    </th>
                    <th>
                        {{ trans('cruds.seo.fields.follow') }}
                    </th>
                    <th>
                        &nbsp;
                    </th>
                </tr>
            </thead>
        </table>
    </div>
</div>



@endsection
@section('scripts')
@parent
<script>
    $(function () {
  let dtButtons = $.extend(true, [], $.fn.dataTable.defaults.buttons)
@can('seo_delete')
  let deleteButtonTrans = '{{ trans('global.datatables.delete') }}';
  let deleteButton = {
    text: deleteButtonTrans,
    url: "{{ route('admin.seos.massDestroy') }}",
    className: 'btn-danger',
    action: function (e, dt, node, config) {
      var ids = $.map(dt.rows({ selected: true }).data(), function (entry) {
          return entry.id
      });

      if (ids.length === 0) {
        alert('{{ trans('global.datatables.zero_selected') }}')

        return
      }

      if (confirm('{{ trans('global.areYouSure') }}')) {
        $.ajax({
          headers: {'x-csrf-token': _token},
          method: 'POST',
          url: config.url,
          data: { ids: ids, _method: 'DELETE' }})
          .done(function () { location.reload() })
      }
    }
  }
  dtButtons.push(deleteButton)
@endcan

  let dtOverrideGlobals = {
    buttons: dtButtons,
    processing: true,
    serverSide: true,
    retrieve: true,
    aaSorting: [],
    ajax: "{{ route('admin.seos.index') }}",
    columns: [
      { data: 'placeholder', name: 'placeholder' },
{ data: 'id', name: 'id' },
{ data: 'meta_title', name: 'meta_title' },
{ data: 'meta_description', name: 'meta_description' },
{ data: 'meta_keywords', name: 'meta_keywords' },
{ data: 'canonical_url', name: 'canonical_url' },
{ data: 'og_image', name: 'og_image', sortable: false, searchable: false },
{ data: 'og_type', name: 'og_type' },
{ data: 'schema_type', name: 'schema_type' },
{ data: 'schema_json', name: 'schema_json' },
{ data: 'sitemap_priority', name: 'sitemap_priority' },
{ data: 'sitemap_frequency', name: 'sitemap_frequency' },
{ data: 'index', name: 'index' },
{ data: 'page_title', name: 'page.title' },
{ data: 'follow', name: 'follow' },
{ data: 'actions', name: '{{ trans('global.actions') }}' }
    ],
    orderCellsTop: true,
    order: [[ 1, 'desc' ]],
    pageLength: 25,
  };
  let table = $('.datatable-Seo').DataTable(dtOverrideGlobals);
  $('a[data-toggle="tab"]').on('shown.bs.tab click', function(e){
      $($.fn.dataTable.tables(true)).DataTable()
          .columns.adjust();
  });
  
});

</script>
@endsection
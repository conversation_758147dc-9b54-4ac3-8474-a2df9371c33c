@extends('layouts.admin')
@section('content')

<div class="card">
    <div class="card-header">
        {{ trans('global.show') }} {{ trans('cruds.seo.title') }}
    </div>

    <div class="card-body">
        <div class="form-group">
            <div class="form-group">
                <a class="btn btn-default" href="{{ route('admin.seos.index') }}">
                    {{ trans('global.back_to_list') }}
                </a>
            </div>
            <table class="table table-bordered table-striped">
                <tbody>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.id') }}
                        </th>
                        <td>
                            {{ $seo->id }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.meta_title') }}
                        </th>
                        <td>
                            {{ $seo->meta_title }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.meta_description') }}
                        </th>
                        <td>
                            {{ $seo->meta_description }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.meta_keywords') }}
                        </th>
                        <td>
                            {{ $seo->meta_keywords }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.canonical_url') }}
                        </th>
                        <td>
                            {{ $seo->canonical_url }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.og_image') }}
                        </th>
                        <td>
                            @if($seo->og_image)
                                <a href="{{ $seo->og_image->getUrl() }}" target="_blank" style="display: inline-block">
                                    <img src="{{ $seo->og_image->getUrl('thumb') }}">
                                </a>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.og_type') }}
                        </th>
                        <td>
                            {{ $seo->og_type }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.schema_type') }}
                        </th>
                        <td>
                            {{ $seo->schema_type }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.schema_json') }}
                        </th>
                        <td>
                            {{ $seo->schema_json }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.sitemap_priority') }}
                        </th>
                        <td>
                            {{ $seo->sitemap_priority }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.sitemap_frequency') }}
                        </th>
                        <td>
                            {{ $seo->sitemap_frequency }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.index') }}
                        </th>
                        <td>
                            <input type="checkbox" disabled="disabled" {{ $seo->index ? 'checked' : '' }}>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.page') }}
                        </th>
                        <td>
                            {{ $seo->page->title ?? '' }}
                        </td>
                    </tr>
                    <tr>
                        <th>
                            {{ trans('cruds.seo.fields.follow') }}
                        </th>
                        <td>
                            <input type="checkbox" disabled="disabled" {{ $seo->follow ? 'checked' : '' }}>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="form-group">
                <a class="btn btn-default" href="{{ route('admin.seos.index') }}">
                    {{ trans('global.back_to_list') }}
                </a>
            </div>
        </div>
    </div>
</div>



@endsection
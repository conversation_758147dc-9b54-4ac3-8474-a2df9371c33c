@extends('layouts.admin')
@section('content')

<div class="card">
    <div class="card-header">
        {{ trans('global.create') }} {{ trans('cruds.setting.title_singular') }}
    </div>

    <div class="card-body">
        <form method="POST" action="{{ route("admin.settings.store") }}" enctype="multipart/form-data">
            @csrf
            <div class="form-group">
                <label for="meta_key">{{ trans('cruds.setting.fields.meta_key') }}</label>
                <input class="form-control {{ $errors->has('meta_key') ? 'is-invalid' : '' }}" type="text" name="meta_key" id="meta_key" value="{{ old('meta_key', '') }}">
                @if($errors->has('meta_key'))
                    <div class="invalid-feedback">
                        {{ $errors->first('meta_key') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.setting.fields.meta_key_helper') }}</span>
            </div>
            <div class="form-group">
                <label for="meta_value">{{ trans('cruds.setting.fields.meta_value') }}</label>
                <input class="form-control {{ $errors->has('meta_value') ? 'is-invalid' : '' }}" type="text" name="meta_value" id="meta_value" value="{{ old('meta_value', '') }}">
                @if($errors->has('meta_value'))
                    <div class="invalid-feedback">
                        {{ $errors->first('meta_value') }}
                    </div>
                @endif
                <span class="help-block">{{ trans('cruds.setting.fields.meta_value_helper') }}</span>
            </div>
            <div class="form-group">
                <button class="btn btn-danger" type="submit">
                    {{ trans('global.save') }}
                </button>
            </div>
        </form>
    </div>
</div>



@endsection
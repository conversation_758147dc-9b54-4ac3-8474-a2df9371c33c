@extends('layouts.admin')
@section('header_title')
    {{ trans('global.list') }} {{ trans('cruds.setting.title_singular') }}
@endsection
@section('styles')
    <link href="{{ url('/') }}/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('global.list') }} {{ trans('cruds.setting.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href=""class="text-muted text-hover-primary">
                            {{ trans('cruds.setting.title_singular') }}
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">
                        {{ trans('global.list') }} {{ trans('cruds.setting.title_singular') }}
                    </li>
                </ul>
            </div>

        </div>
    </div>
@endsection

@section('content')
    <div class="card col-6">
        <div class="card-body">

            <form action="{{ route('admin.settings.update', ['1']) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="form-group mb-3">
                    <label class="form-label" for="site_title">Site Title</label>
                    <input type="text" name="site_title" value="{{ $settings['site_title'] ?? '' }}" class="form-control form-control-sm">
                </div>

                <div class="form-group mb-3">
                    <label class="form-label" for="site_description">Site Description</label>
                    <input type="text" name="site_description" value="{{ $settings['site_description'] ?? '' }}" class="form-control form-control-sm">
                </div>

                {{-- <div class="form-group mb-3">
                    <label class="form-label" for="favicon">Favicon</label>
                    <div class="input-group  flex-nowrap">
                        <div class="overflow-hidden flex-grow-1">
                            <input type="text" name="favicon" value="{{ $settings['favicon'] ?? '' }}" class="form-control form-control-sm rounded-end-0">
                        </div>
                        <a class="btn btn-sm btn-primary" href="{{ route('admin.galleries.index', 1) }}" target="_blank"> <i class="fa-solid fa-upload"></i></a>
                    </div>
                </div> --}}

                <div class="form-group mb-3">
                    <label class="form-label" for="logo">Logo</label>
                    <div class="input-group  flex-nowrap">
                        <div class="overflow-hidden flex-grow-1">
                            <input type="text" name="logo" value="{{ $settings['logo'] ?? '' }}" class="form-control form-control-sm rounded-end-0">
                        </div>
                        <a class="btn btn-sm btn-primary" href="{{ route('admin.galleries.index', 1) }}" target="_blank"> <i class="fa-solid fa-upload"></i></a>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label class="form-label" for="seo-banner">Seo Banner</label>
                    <div class="input-group  flex-nowrap">
                        <div class="overflow-hidden flex-grow-1">
                            <input type="text" name="seo-banner" value="{{ $settings['seo-banner'] ?? '' }}" class="form-control form-control-sm rounded-end-0">
                        </div>
                        <a class="btn btn-sm btn-primary" href="{{ route('admin.galleries.index', 1) }}" target="_blank"> <i class="fa-solid fa-upload"></i></a>
                    </div>
                </div>


                <div class="form-group mb-3">
                    <label class="form-label" for="owner_name">Owner Name</label>
                    <input type="text" name="owner_name" value="{{ $settings['owner_name'] ?? '' }}" class="form-control form-control-sm">
                </div>

                <div class="form-group mb-3">
                    <label class="form-label" for="owner_contact_phone">Owner Contact Phone</label>
                    <input type="text" name="owner_contact_phone" value="{{ $settings['owner_contact_phone'] ?? '' }}" class="form-control form-control-sm">
                </div>

                <button type="submit" class="btn btn-primary btn-sm">
                    <i class="fa-solid fa-save"></i> Save
                </button>
            </form>




        </div>
    </div>
@endsection

@section('scripts')
@endsection

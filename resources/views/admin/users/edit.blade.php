@extends('layouts.admin')

@section('header_title')
    {{ trans('global.edit') }} {{ trans('cruds.user.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.user.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="{{ route('admin.users.index') }}" class="text-muted text-hover-primary">{{ trans('cruds.user.title_singular') }}</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">{{ trans('global.edit') }}
                        {{ trans('cruds.user.title_singular') }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="container-xxxl">
                <div class="row g-0">
                    <div class="col-lg-6 col-6 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4> {{ trans('global.edit') }} {{ trans('cruds.user.title_singular') }}</h4>
                            </div>
                            <div class="card-body p-0">
                                <form method="POST" action="{{ route('admin.users.update', [$user->id]) }}" enctype="multipart/form-data">
                                    @method('PUT')
                                    @csrf
                                    <div class="row">
                                        <div class="form-group mb-3">
                                            <label class="form-label required" for="name">{{ trans('cruds.user.fields.name') }}</label>
                                            <input class="form-control form-control-sm {{ $errors->has('name') ? 'is-invalid' : '' }}" type="text" name="name" id="name" value="{{ old('name', $user->name) }}" required>
                                            @if ($errors->has('name'))
                                                <div class="invalid-feedback">
                                                    {{ $errors->first('name') }}
                                                </div>
                                            @endif
                                            <span class="help-block">{{ trans('cruds.user.fields.name_helper') }}</span>
                                        </div>
                                        <div class="form-group mb-3">
                                            <label class="required form-label" for="email">{{ trans('cruds.user.fields.email') }}</label>
                                            <input class="form-control form-control-sm {{ $errors->has('email') ? 'is-invalid' : '' }}" type="email" name="email" id="email" value="{{ old('email', $user->email) }}" required>
                                            @if ($errors->has('email'))
                                                <div class="invalid-feedback">
                                                    {{ $errors->first('email') }}
                                                </div>
                                            @endif
                                            <span class="help-block">{{ trans('cruds.user.fields.email_helper') }}</span>
                                        </div>

                                        @if (Auth::user()->is_admin)
                                            <div class="form-group mb-3">
                                                <label class="form-label required" for="roles">{{ trans('cruds.user.fields.roles') }}</label>
                                                <select class="form-select form-select-sm {{ $errors->has('roles') ? 'is-invalid' : '' }}" name="roles[]" id="roles" data-control="select2" multiple required>
                                                    @foreach ($roles as $id => $role)
                                                        <option value="{{ $id }}" {{ in_array($id, old('roles', [])) || $user->roles->contains($id) ? 'selected' : '' }}>{{ $role }}</option>
                                                    @endforeach
                                                </select>
                                                @if ($errors->has('roles'))
                                                    <div class="invalid-feedback">
                                                        {{ $errors->first('roles') }}
                                                    </div>
                                                @endif
                                                <span class="help-block">{{ trans('cruds.user.fields.roles_helper') }}</span>
                                            </div>
                                        @else

                                            <input type="hidden" name="roles[]" value="2">

                                        @endif

                                        <div class="form-group mb-3">
                                            <label class="form-label" for="password">New {{ trans('cruds.user.fields.password') }}</label>
                                            <input class="form-control form-control-sm {{ $errors->has('password') ? 'is-invalid' : '' }}" type="password" name="password" id="password">
                                            @if ($errors->has('password'))
                                                <div class="invalid-feedback">
                                                    {{ $errors->first('password') }}
                                                </div>
                                            @endif
                                            <span class="help-block">{{ trans('cruds.user.fields.password_helper') }}</span>
                                        </div>
                                        <div class="form-group mb-3 mb-3">
                                            <button class="btn btn-sm btn-danger" type="submit">
                                                {{ trans('global.save') }}
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
   
@endsection

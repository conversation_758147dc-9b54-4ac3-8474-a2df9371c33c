@extends('layouts.app')

@section('header_title')
    {{ trans('global.login') }}
@endsection


@section('content')
    <div class="d-flex flex-column flex-lg-row flex-column-fluid">

        <div class="d-flex flex-lg-row-fluid">

            <div class="d-flex flex-column flex-center pb-0 pb-lg-10 p-10 w-100">

                <img class="theme-light-show mx-auto mw-100 w-150px w-lg-300px mb-10 mb-lg-20" src="{{ url('/') }}/assets/images/logo-salty-strike.png" alt="" />
                <img class="theme-dark-show mx-auto mw-100 w-150px w-lg-300px mb-10 mb-lg-20" src="{{ url('/') }}/assets/images/logo-salty-strike-white.png" alt="" />

                {{-- <h1 class="text-gray-800 fs-2qx fw-bold text-center mb-7">
                    {{ trans('panel.site_title') }}
                </h1> --}}

                <div class="text-gray-600 fs-base text-center fw-semibold w-500px">
                    Welcome to the Back Office, where you can efficiently manage your website by editing and adding page content, optimizing SEO, handling contact forms and contact lists, and overseeing admin users.
                </div>
            </div>

        </div>

        <div class="d-flex flex-column-fluid flex-lg-row-auto justify-content-center justify-content-lg-end p-12">

            <div class="bg-body d-flex flex-column flex-center rounded-4 w-md-600px p-10">

                <div class="d-flex flex-center flex-column align-items-stretch h-lg-100 w-md-400px">

                    <div class="d-flex flex-center flex-column flex-column-fluid pb-15 pb-lg-20">


                        <form class="form w-100" method="POST" action="{{ route('login') }}">
                            @csrf
                            <div class="text-center mb-11">

                                <h1 class="text-gray-900 fw-bolder mb-3">
                                    Login to your account
                                </h1>
                                <div class="text-gray-500 fw-semibold fs-6">
                                    {{ trans('panel.site_title') }} Back Office
                                </div>

                            </div>

                            @if (session('message'))
                                <div class="alert alert-info" role="alert">
                                    {{ session('message') }}
                                </div>
                            @endif

                            <div class="fv-row mb-8">
                                <input id="email" name="email" type="text" class="form-control bg-transparent {{ $errors->has('email') ? ' is-invalid' : '' }}" required autocomplete="email" autofocus placeholder="{{ trans('global.login_email') }}" value="{{ old('email', null) }}">


                            </div>

                            <div class="fv-row mb-5">
                                <input id="password" name="password" type="password" class="form-control bg-transparent {{ $errors->has('email') ? ' is-invalid' : '' }}" required placeholder="{{ trans('global.login_password') }}">

                                @if ($errors->has('email'))
                                    <div class="invalid-feedback">
                                        {{ $errors->first('email') }}
                                    </div>
                                @endif
                            </div>

                            <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8 mt-4">
                                <div>
                                    <input class="form-check-input" name="remember" type="checkbox" id="remember" style="vertical-align: middle;" />
                                    <label class="form-check-label" style="vertical-align: middle;">
                                        I Accept the
                                        <a  href="{{ url('/') }}" class="link-primary color-lt">
                                            Privacy Policy
                                        </a>
                                    </label>
                                </div>

                            </div>

                            <div class="d-grid mb-10">
                                <button type="submit"class="btn btn-primary">
                                    {{ trans('global.login') }}
                                </button>
                            </div>

                            <div class="text-gray-500 text-center fw-semibold fs-6">
                                @if (Route::has('password.request'))
                                    {{ trans('global.forgot_password') }}
                                    <a class="link-primary color-lt" href="{{ route('password.request') }}">
                                        Reset Password
                                    </a><br>
                                @endif
                            </div>

                        </form>

                    </div>


                </div>
            </div>
        </div>
    </div>
@endsection

{{-- @extends('layouts.app')
@section('content')
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card mx-4">
            <div class="card-body p-4">
                <h1>{{ trans('panel.site_title') }}</h1>

                <p class="text-muted">{{ trans('global.login') }}</p>

                @if (session('message'))
                    <div class="alert alert-info" role="alert">
                        {{ session('message') }}
                    </div>
                @endif

                <form method="POST" action="{{ route('login') }}">
                    @csrf

                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text">
                                <i class="fa fa-user"></i>
                            </span>
                        </div>

                        <input id="email" name="email" type="text" class="form-control{{ $errors->has('email') ? ' is-invalid' : '' }}" required autocomplete="email" autofocus placeholder="{{ trans('global.login_email') }}" value="{{ old('email', null) }}">

                        @if ($errors->has('email'))
                            <div class="invalid-feedback">
                                {{ $errors->first('email') }}
                            </div>
                        @endif
                    </div>

                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-lock"></i></span>
                        </div>

                        <input id="password" name="password" type="password" class="form-control{{ $errors->has('password') ? ' is-invalid' : '' }}" required placeholder="{{ trans('global.login_password') }}">

                        @if ($errors->has('password'))
                            <div class="invalid-feedback">
                                {{ $errors->first('password') }}
                            </div>
                        @endif
                    </div>

                    <div class="input-group mb-4">
                        <div class="form-check checkbox">
                            <input class="form-check-input" name="remember" type="checkbox" id="remember" style="vertical-align: middle;" />
                            <label class="form-check-label" for="remember" style="vertical-align: middle;">
                                {{ trans('global.remember_me') }}
                            </label>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <button type="submit" class="btn btn-primary px-4">
                                {{ trans('global.login') }}
                            </button>
                        </div>
                        <div class="col-6 text-right">
                            @if (Route::has('password.request'))
                                <a class="btn btn-link px-0" href="{{ route('password.request') }}">
                                    {{ trans('global.forgot_password') }}
                                </a><br>
                            @endif
                            <a class="btn btn-link px-0" href="{{ route('register') }}">
                                {{ trans('global.register') }}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection --}}

<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ trans('panel.site_description') }} </title>
    <meta name="description" content="{{ $meta_description }}">
    <meta name="keywords" content="{{ $meta_keywords }}">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="{{ url('/') }}">
    <link rel="icon" type="image/png" href="/assets/images/favicon/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/assets/images/favicon/favicon.svg" />
    <link rel="shortcut icon" href="/assets/images/favicon/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/favicon/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="Salty Strike Mauritius" />
    <link rel="manifest" href="/assets/images/favicon/site.webmanifest" />

    <meta property="og:title" content="{{ $settings['site_title'] ?? trans('panel.site_description') }}">
    <meta property="og:description" content="{{ $meta_description }}">
    <meta property="og:image" content="{{ trans('panel.seo-banner') }}">
    <meta property="og:url" content="{{ url('/') }}">
    <meta property="og:type" content="website">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $settings['site_title'] ?? trans('panel.site_description') }}">
    <meta name="twitter:description" content="{{ $meta_description }}">
    <meta name="twitter:image" content="{{ trans('panel.seo-banner') }}">

    <script type="application/ld+json">
		{
		"@context": "https://schema.org",
		"@type": "Organization",
		"name": "{{ $settings['site_title'] ?? trans('panel.site_description') }}",
		"url": "{{ url('/') }}",
		"logo": "{{ $settings['logo'] ?? trans('panel.logo') }}",
		"description": "{{ $meta_description }}",
		"founder": {
			"@type": "Person",
			"name": "{{ $settings['owner_name'] ?? trans('panel.owner_name') }}"
		},
		"contactPoint": {
			"@type": "ContactPoint",
			"telephone": "{{ $settings['owner_contact_phone'] ?? trans('panel.owner_contact_phone') }}",
			"contactType": "customer service",
			"areaServed": "MU"
		}
		}
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Baskervville:ital@0;1&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">

    <link href="./output.css" rel="stylesheet" />
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.6.5/flowbite.min.js"></script>
    <link rel="stylesheet" href="{{ asset('css/output.css') }}?v={{ rand(1000,9999) }}">
    <link rel="stylesheet" href="{{ asset('css/basic.css') }}?v={{ rand(1000,9999) }}">
</head>

<body class="bg-[url('{{ asset('images/' . $page->bg) }}')] bg-cover bg-center @if ($page->id == 4 || $page->id == 5 || $page->id == 9) bg-top @endif min-h-screen flex flex-col">

    @include('frontend.section.header', ['section_header' => $section_header])

    @if ($page->id == 1 || $page->id == 2 || $page->id == 3)
        <main class="flex-1 flex justify-end items-center px-4 sm:px-6 lg:px-24">
    @elseif ($page->id == 6 || $page->id == 10)
        <main class="min-h-screen pt-10 container-inner">
    @else
        <main class="min-h-screen container-inner">
    @endif

    @include('frontend.page.' . $page->admin_page, ['section_data' => $section_data])

    @include('frontend.section.footer', ['section_footer' => $section_footer])
    </main>

    <div id="successAlert" class="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 hidden">
        <div class="bg-white rounded-md shadow p-6 max-w-sm w-full">
            <h2 class="text-2xl font-semibold mb-4">{{ app()->getLocale() == 'en' ? 'Success' : 'Succès' }}</h2>
            <p class="mb-4">{{ app()->getLocale() == 'en' ? 'Your message has been sent successfully!' : 'Votre message a été envoyé avec succès!' }}</p>
            <button id="closeAlert" class="inline-block px-4 py-2 bg-[#0caca0] text-white rounded hover:bg-[#0c567d]">
                {{ app()->getLocale() == 'en' ? 'Close' : 'Fermer' }}
            </button>
        </div>
    </div>

    {{-- jQuery --}}
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    {{-- Scroll to section --}}
    <script>
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
            anchor.addEventListener("click", function(e) {
                e.preventDefault();

                document.querySelector(this.getAttribute("href")).scrollIntoView({
                    behavior: "smooth",
                    block: "start",
                });
            });
        });
    </script>

    {{-- Contact Form --}}
    <script>
        $(document).ready(function() {
            $('#contact-form-submit').on('click', function(e) {
                e.preventDefault();

                let nameVal = $('input[name="name"]').val();
                let emailVal = $('input[name="email"]').val();
                let messageVal = $('textarea[name="message"]').val();

                $.ajax({
                    url: "{{ route('contact.save') }}",
                    type: 'POST',
                    data: {
                        name: nameVal,
                        email: emailVal,
                        message: messageVal,
                        _token: "{{ csrf_token() }}",
                    },
                    success: function(response) {
                        $('#successAlert').removeClass('hidden');
                        $('#contact-form')[0].reset();
                    },
                    error: function(xhr, status, error) {
                        alert("{{ app()->getLocale() == 'en' ? 'An error occurred. Please try again later.' : 'Une erreur s\'est produite. Veuillez réessayer plus tard.' }}");
                    }
                });
            });

            $('#closeAlert').on('click', function() {
                $('#successAlert').addClass('hidden');
            });
        });
    </script>
</body>

</html>

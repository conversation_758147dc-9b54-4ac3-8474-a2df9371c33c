<style>
    @keyframes ping-slow {
        0% {
            transform: scale(1);
            opacity: 1;
        }

        70% {
            transform: scale(2);
            opacity: 0;
        }

        100% {
            transform: scale(2);
            opacity: 0;
        }
    }

    .animate-ping-slow {
        animation: ping-slow 2.5s cubic-bezier(0, 0, 0.2, 1) infinite;
    }
</style>

<div data-section="localisations">
    <div class=" flex items-center justify-center text-3xl text-custombrown static">
        {{ $section_data->title_1 }}
    </div>
    <div class="flex items-center justify-center text-sm text-customgray">
        {!! $section_data->description_1 !!}
    </div>
    <section class="relative w-full max-w-5xl mx-auto overflow-hidden" style="aspect-ratio: 2.2/1;">
        <!-- MAP BACKGROUND -->
        <img src="{{ asset('images/map.png') }}" alt="Dotted world map" class="absolute inset-0 w-full h-full object-contain object-center pointer-events-none select-none" />


        @foreach ($section_data->list_1 as $value)
            @php
                $class = 'absolute top-2 left-20 -translate-x-full -translate-y-full whitespace-nowrap';
                if ($value['url'] == '/madagascar') {
                    $class = 'absolute top-0 right-0 translate-x-full -translate-y-full mt-4 whitespace-nowrap';
                }
                if ($value['url'] == '/maurice') {
                    $class = 'absolute top-0 right-0 translate-x-full -translate-y-full mt-5 whitespace-nowrap';
                }
                if ($value['url'] == '/reunion') {
                    $class = 'absolute top-full mt-2 ml-14 left-1/2 -translate-x-1/2 whitespace-nowrap';
                }
                if ($value['url'] == '/nouvellecaledonie') {
                    $class = 'absolute left-full ml-3 top-1/2 -translate-y-1/2 whitespace-nowrap';
                }
            @endphp
            <a href="{{ $value['url'] }}" class="absolute -translate-x-1/2 -translate-y-full" style="{{ $value['style'] }}">
                <span class="relative block">
                    <span class="absolute inset-0 animate-ping-slow rounded-full bg-blue-900 opacity-75"></span>
                    <span class="relative flex h-6 w-6 items-center justify-center">
                        <img src="@if ($value['url'] == '/comores') {{ asset('images/pin2.png') }} @else {{ asset('images/pin.png') }} @endif" alt="Map Pin" />
                    </span>
                    <span class="{{ $class }}">
                        {{ $value['title'] }}
                    </span>
                </span>
            </a>
        @endforeach

    </section>

    <div class="flex flex-col sm:flex-row sm:justify-between items-center w-full mb-5 px-4 gap-4">
        <!-- Left section -->
        <div class="flex flex-col sm:flex-row gap-4 sm:justify-start items-center">
            @foreach ($section_data->list_2 as $value)
                @php
                    $image = 'images/pin.png';
                    if ($value['url'] == '3') {
                        $image = 'images/pin2.png';
                    }
                    if ($value['url'] == '2') {
                        $image = 'images/partenaire.png';
                    }

                @endphp
                <img src="{{ asset($image) }}" alt="Pin"><span>{{ $value['title'] }}</span>
            @endforeach
        </div>

        <!-- Right section -->
        <div class="flex gap-4 w-full sm:w-auto sm:min-w-[300px] justify-center sm:justify-end">
            @foreach ($section_data->list_3 as $value)
                <div class="w-40 rounded-full p-[4px] bg-gradient-to-r from-[#fcef50] to-[#98c254]">
                    <a href="{{ $value['url'] }}" class="flex items-center justify-center w-full h-full px-4 py-2 bg-gray-700 text-white rounded-full text-sm font-semibold text-center">
                        <span>{!! $value['title'] !!}</span>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</div>

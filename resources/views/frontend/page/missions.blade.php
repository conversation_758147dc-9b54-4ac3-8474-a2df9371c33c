

<div data-section="missions" class="w-full lg:w-1/2 space-y-6">
    <section class="px-4 text-center lg:text-right">
        <!-- Quote -->
        <h2 class="text-4xl sm:text-3xl md:text-4xl text-customgray font-light leading-relaxed">
            {{ $section_data->title_1 }}
            <br>
            {{ $section_data->title_2 }}
        </h2>
        <div class="text-right mt-7">
            <ul class="space-y-3">
                @foreach ($section_data->list_1 as $value)
                    <li class="flex items-start justify-end gap-2">
                        <span class="text-customgray text-2xl">{{ $value['title'] }}</span>
                        <img src="{{ asset('images/bulletR.svg') }}" class="w-[20px] mt-3" alt="bullet" />
                    </li>
                @endforeach
            </ul>
        </div>
        <!-- Listening info -->
        <p class="mt-8 text-xl md:text-3xl text-customgray text-center lg:text-right">
            {{ $section_data->title_3 }}
        </p>
        <!-- Buttons -->
        <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center lg:justify-end">
            @foreach ($section_data->list_2 as $value)
                <div class="rounded-full p-[4px] bg-gradient-to-r from-[#fcef50] to-[#98c254">
                    <a href="{{ $value['url'] }}" class="flex items-center justify-center px-8 py-2 bg-gray-700 text-white rounded-full text-sm font-semibold text-center w-full h-full">
                        <span>{!! $value['title'] !!}</span>
                    </a>
                </div>
            @endforeach
        </div>
        <!-- En savoir plus -->
        <div class="mt-10 text-sm text-center lg:text-right">

            @foreach ($section_data->list_3 as $value)
                <a href="{{ $value['url'] }}" class="text-customgray font-medium text-lg inline-flex items-center gap-2 justify-center lg:justify-end">
                    <img src="{{ asset('images/bullet.svg') }}" class="w-[20px]" alt="bullet" />
                    <span>{!! $value['title'] !!}</span>
                </a>
            @endforeach
        </div>
    </section>
</div>

{{-- <header data-section="header" class="sticky top-0 left-0 w-full bg-black text-white py-4 px-6 h-24 flex items-center z-50">
    <div class="text-xl font-bold">
        <img class="w-20" src="{{ $section_header->logo }}" alt="">
    </div>

    <nav class="hidden md:flex space-x-6 flex-grow justify-center">
        @foreach ($section_header->menu_list as $key => $value)
            <a href="{{ $value['link'] }}" class="hover:text-gray-400 uppercase">{{ $value['name'] }}</a>
        @endforeach
    </nav>

    <div class="absolute right-6 flex space-x-4 items-center">
        @foreach ($section_header->social_list as $key => $value)
            <a href="{{ $value['link'] }}" target="_blank" class="hover:text-gray-400">
                <img class="w-10" src="{{ $value['image'] }}" alt="">
            </a>
        @endforeach

        <div class="relative group">
            <button class="flex items-center space-x-2 bg-gray-800 px-3 py-2 rounded-md">
                <span id="current-lang" class="uppercase">{{ app()->getLocale() }}</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>

            <div class="absolute right-0 w-24 bg-white text-black rounded-md shadow-lg hidden group-hover:block">
                <a href="{{ route('change-language', 'fr') }}" class="block px-4 py-2 hover:bg-gray-200">Français</a>
                <a href="{{ route('change-language', 'en') }}" class="block px-4 py-2 hover:bg-gray-200">English</a>
            </div>
        </div>
    </div>
</header> --}}


<header data-section="header" class="w-full px-8 py-4 bg-black bg-opacity-5">
    <div class="flex justify-center w-full">
        <nav class="hidden md:flex justify-center w-full">
            <ul class="flex items-center space-x-10 text-2xl font-medium text-gray-700">
                @php
                    $totalItems = count($section_header->menu_list);
                    $middlePoint = ceil($totalItems / 2);
                @endphp

                @foreach ($section_header->menu_list as $key => $value)
                    <li><a href="{{ $value['link'] }}">{{ $value['name'] }}</a></li>

                    @if ($key + 1 == $middlePoint)
                        <li>
                            <a href="{{ route('home') }}"><img src="{{ $section_header->logo }}" alt="Logo" class="h-28" /></a>
                        </li>
                    @endif
                @endforeach
            </ul>
        </nav>
    </div>
</header>

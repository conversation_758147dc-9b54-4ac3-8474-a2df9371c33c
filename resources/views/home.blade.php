@extends('layouts.admin')

@section('header_title', 'Dashboard')

@section('content')
    <style>
        .card_number {
            font-size: 5rem;
            color: #00ABC8;
        }
    </style>
    <div class="content">
        <div class="row">

            <div class="col-lg-3">
                <a href="{{ route('admin.contact-messages.index') }}" class="text-decoration-none">
                    <div class="card card-flush">
                        <div class="card-body pt-5 text-center">
                            <div>
                                <p class="card_number fw-bolder">{{ $messages_read }}</p>
                                <p class="fs-4">Messages Read</p>
                            </div>
                        </div>

                    </div>
                </a>
            </div>

            <div class="col-lg-3">
                <a href="{{ route('admin.contact-messages.index') }}" class="text-decoration-none">
                    <div class="card card-flush">
                        <div class="card-body pt-5 text-center">
                            <div>
                                <p class="card_number fw-bolder">{{ $messages_unread }}</p>
                                <p class="fs-4">Messages Unread</p>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

        </div>

        <div class="row">

            <div class="col-12 mt-5">
                <div class="card card-flush h-xl-100">

                    <div class="card-header pt-7">

                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold text-gray-900">Last 10 Messages</span>
                        </h3>


                    </div>

                    <div class="card-body">
                        <!--begin::Table-->
                        <div id="kt_table_widget_5_table_wrapper" class="dt-container dt-bootstrap5 dt-empty-footer">
                            <div id="" class="table-responsive">
                                <table class="table align-middle table-row-dashed fs-6 gy-3 dataTable" id="kt_table_widget_5_table" style="width: 100%;">

                                    <thead>

                                        <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                                            <th>
                                                {{ trans('cruds.contactMessage.fields.id') }}
                                            </th>
                                            <th>
                                                {{ trans('cruds.contactMessage.fields.name') }}
                                            </th>
                                            <th>
                                                {{ trans('cruds.contactMessage.fields.email') }}
                                            </th>
                                            <th>
                                                {{ trans('cruds.contactMessage.fields.message') }}
                                            </th>
                                            <th>
                                                {{ trans('cruds.contactMessage.fields.status') }}
                                            </th>
                                            <th>
                                                {{ trans('cruds.contactMessage.fields.created_at') }}
                                            </th>
                                            <th class="text-center">
                                                ACTIONS
                                            </th>
                                        </tr>

                                    </thead>
                                    <!--end::Table head-->

                                    <!--begin::Table body-->
                                    <tbody class="fw-bold text-gray-600">
                                        @foreach ($messages as $key => $message)
                                            <tr data-entry-id="{{ $message->id }}">

                                                <td>
                                                    {{ $message->id ?? '' }}
                                                </td>

                                                <td>
                                                    {{ $message->name ?? '' }}
                                                </td>
                                                <td>
                                                    {{ $message->email ?? '' }}
                                                </td>
                                                <td>
                                                    {{ $message->message ?? '' }}
                                                </td>
                                                <td>
                                                    <span class="badge py-3 px-4 fs-7 badge-light-{{ $message->status == 'read' ? 'success' : 'primary' }}">
                                                        {{ App\Models\ContactMessage::STATUS_SELECT[$message->status] ?? '' }}
                                                    </span>
                                                </td>

                                                <td>
                                                    {{ $message->created_at ?? '' }}
                                                </td>

                                                <td class="text-center">
                                                    @can('contact_message_show')
                                                        <a class="btn btn-icon btn-active-light-primary w-30px h-30px me-3" href="{{ route('admin.contact-messages.show', $message->id) }}">
                                                            <span>
                                                                <i class="fa-solid fa-eye"></i>
                                                            </span>
                                                        </a>
                                                    @endcan
                                                </td>

                                            </tr>
                                        @endforeach

                                    </tbody>

                                </table>
                            </div>

                        </div>
                        <!--end::Table-->
                    </div>
                    <!--end::Card body-->
                </div>
            </div>

        </div>

    </div>
@endsection
@section('scripts')
    @parent
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/modules/exporting.js"></script>
    <script src="https://code.highcharts.com/modules/export-data.js"></script>
    <script src="https://code.highcharts.com/modules/accessibility.js"></script>
    <script src="{{ url('/') }}/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js"></script>
@endsection

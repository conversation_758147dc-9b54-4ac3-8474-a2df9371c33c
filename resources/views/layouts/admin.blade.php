<!DOCTYPE html>
<html lang="en">

<!--begin::Head-->
<head>
	<title>{{ trans('panel.site_title') }} - @yield('header_title')</title>
	<meta charset="utf-8" />
	<meta name="description" content="{{ trans('panel.site_title') }}" />
	<meta name="keywords" content="{{ trans('panel.site_title') }}" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta property="og:locale" content="en_US" />
	<meta property="og:type" content="article" />
	<meta property="og:title" content="{{ trans('panel.site_title') }}" />
	<meta property="og:url" content="{{ url('/') }}" />
	<meta property="og:site_name" content="{{ trans('panel.site_title') }}" />
	<link rel="canonical" href="{{ url('/') }}" />
	<link rel="shortcut icon" href="{{ url('/') }}/" />
	<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
	<link href="{{ url('/') }}/assets/plugins/custom/fullcalendar/fullcalendar.bundle.css" rel="stylesheet" type="text/css" />
	<link href="{{ url('/') }}/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
	<link href="{{ url('/') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
	<link href="{{ url('/') }}/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
	<script src="{{ url('/') }}/assets/js/scripts.bundle.js"></script>

    <meta name="csrf-token" content="{{ csrf_token() }}">

    @yield('styles')
    <link href="{{ url('/') }}/assets/css/custom.css?v={{ rand(1000,9999) }}" rel="stylesheet" type="text/css" />
</head>
<!--end::Head-->

<!--begin::Body-->

<body id="kt_app_body" data-kt-app-layout="light-sidebar" data-kt-app-header-fixed="true" data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true" data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true" data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" class="app-default">

	<!--begin::Theme mode setup on page load-->
    @include('layouts.common.theme_mode')
	<!--end::Theme mode setup on page load-->

	<!--begin::App-->
	<div class="d-flex flex-column flex-root app-root" id="kt_app_root">
		<!--begin::Page-->
		<div class="app-page flex-column flex-column-fluid" id="kt_app_page">

			<!--begin::Header-->
			@include('layouts.common.header')
			<!--end::Header-->

			<!--begin::Wrapper-->
			<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
				<!--begin::Sidebar-->
				@include('layouts.common.sidebar')
				<!--end::Sidebar-->

				<!--begin::Main-->
				<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
					<!--begin::Content wrapper-->
					<div class="d-flex flex-column flex-column-fluid">

                        @if($errors->count() > 0)
                            <div class="alert alert-danger mx-10 mb-0">
                                <ul class="list-unstyled">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

						<!--begin::Toolbar-->
                        @yield('toolbar')
						<!--end::Toolbar-->

						<!--begin::Content-->
						<div id="kt_app_content" class="app-content flex-column-fluid">
							<!--begin::Content container-->
							<div id="kt_app_content_container" class="app-container container-fluid pt-5">
								<!--begin::Row-->
                                    @yield('content')
								<!--end::Row-->
							</div>
							<!--end::Content container-->
						</div>
						<!--end::Content-->

					</div>
					<!--end::Content wrapper-->

					<!--begin::Footer-->
					@include('layouts.common.footer')
					<!--end::Footer-->

				</div>
				<!--end:::Main-->
			</div>
			<!--end::Wrapper-->
		</div>
		<!--end::Page-->
	</div>
	<!--end::App-->

	<!--begin::Scrolltop-->
    @include('layouts.common.scrolltop')
	<!--end::Scrolltop-->

	<!--begin::Global Javascript Bundle(mandatory for all pages)-->
    @include('layouts.common.script')
	<!--end::Vendors Javascript-->

	<!--begin::Custom Javascript(used for this page only)-->
    @yield('scripts')
	<!--end::Custom Javascript-->

    <form id="logoutform" action="{{ route('logout') }}" method="POST" style="display: none;">
        {{ csrf_field() }}
    </form>
</body>
<!--end::Body-->

</html>

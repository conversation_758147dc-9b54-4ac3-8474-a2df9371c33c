<script src="{{ url('/') }}/assets/plugins/global/plugins.bundle.js"></script>
<script src="{{ url('/') }}/assets/js/scripts.bundle.js"></script>
<!--end::Global Javascript Bundle-->
<!--begin::Vendors Javascript(used for this page only)-->
<script src="{{ url('/') }}/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js"></script>
<script src="https://cdn.amcharts.com/lib/5/index.js"></script>
<script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
<script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
<script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
<script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
<script src="https://cdn.amcharts.com/lib/5/map.js"></script>
<script src="https://cdn.amcharts.com/lib/5/geodata/worldLow.js"></script>
<script src="https://cdn.amcharts.com/lib/5/geodata/continentsLow.js"></script>
<script src="https://cdn.amcharts.com/lib/5/geodata/usaLow.js"></script>
<script src="https://cdn.amcharts.com/lib/5/geodata/worldTimeZonesLow.js"></script>
<script src="https://cdn.amcharts.com/lib/5/geodata/worldTimeZoneAreasLow.js"></script>
<script src="{{ url('/') }}/assets/plugins/custom/datatables/datatables.bundle.js"></script>
<script>
    $(function() {

        let csvButtonTrans = '{{ trans('global.datatables.csv') }}'
        let excelButtonTrans = '{{ trans('global.datatables.excel') }}'

        let languages = {
            'en': 'https://cdn.datatables.net/plug-ins/1.10.19/i18n/English.json'
        };

        $.extend(true, $.fn.dataTable.Buttons.defaults.dom.button, {
            className: 'btn'
        })

        $.extend(true, $.fn.dataTable.defaults, {
            language: {
                sSearch: "",
                url: languages['{{ app()->getLocale() }}']
            },
            columnDefs: [{
                orderable: true,
                className: '',
                targets: 0
            }, {
                orderable: false,
                searchable: false,
                targets: -1
            }],
            select: {
                style: 'multi+shift',
                selector: 'td:first-child'
            },
            order: [],
            scrollX: true,
            pageLength: 100,
            dom: "<'row'<'col-sm-12 col-md-4'f><'col-sm-12 col-md-8 d-flex align-items-center justify-content-end'B>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-12 col-md-6 d-flex  align-items-center'li><'col-sm-12 col-md-6'p>>" +
                "<'actions text-end'>",

            buttons: [{
                    extend: 'csv',
                    className: 'btn btn-light-warning btn-sm hover-scale me-1',
                    text: csvButtonTrans,
                    exportOptions: {
                        columns: ':visible'
                    }
                },
                {
                    extend: 'excel',
                    className: 'btn btn-light-success btn-sm hover-scale me-1',
                    text: excelButtonTrans,
                    exportOptions: {
                        columns: ':visible'
                    }
                }

            ]
        });

        $.fn.dataTable.ext.classes.sPageButton = '';
    });
</script>


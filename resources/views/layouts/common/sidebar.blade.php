<div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <!--begin::Logo-->
    @if (env('HIDE_LOGO') == false)
        <div class="app-sidebar-logo px-6 col-sm-12 justify-content-center"" id="kt_app_sidebar_logo">
            <a href="/">
                <img class="theme-light-show mx-auto mw-100 w-100px w-lg-100px mb-10 mb-lg-20 mt-20" src="{{ url('/') }}/assets/images/logo-salty-strike.png" alt="" />
                <img class="theme-dark-show mx-auto mw-100 w-100px w-lg-100px mb-10 mb-lg-20 mt-20" src="{{ url('/') }}/assets/images/logo-salty-strike-white.png" alt="" />

            </a>

            <div id="kt_app_sidebar_toggle" class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="app-sidebar-minimize">
                <i class="ki-outline ki-black-left-line fs-3 rotate-180 text-white"></i>
            </div>
            <!--end::Sidebar toggle-->
        </div>
        @endenv

        <!--end::Logo-->
        <!--begin::sidebar menu-->
        <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
            <!--begin::Menu wrapper-->
            <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper">
                <!--begin::Scroll wrapper-->
                <div id="kt_app_sidebar_menu_scroll" class="scroll-y my-5 mx-3" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
                    <!--begin::Menu-->
                    <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6" id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">
                        <div class="col-inner-top col-sm-12">
                            <div class="cnCol">
                                <p class="top">Hello,</p>
                                <p class="name">
                                    {!! str_replace(' ', '<br>', Auth::user()->name) !!}
                                </p>
                                <p class="title">
                                    {{ Auth::user()->roles->first()->title }}
                                </p>
                            </div>
                            <div class="cnCol">
                                @php
                                    use Carbon\Carbon;

                                    $currentDate = Carbon::now();
                                    $dayName = $currentDate->format('l');
                                    $dayNumber = $currentDate->format('d');
                                    $monthYear = $currentDate->format('F Y');
                                @endphp

                                <p class="day">{{ strtoupper($dayName) }}</p>
                                <p class="date">{{ $dayNumber }}</p>
                                <p class="month">{{ strtoupper($monthYear) }}</p>
                            </div>

                        </div>
                        <!--begin:Menu item-->
                        <div class="menu-item pt-5">
                            <!--begin:Menu content-->
                            <div class="menu-content menu-dash">
                                <a href="{{ route('admin.home') }}"><span class="menu-heading fw-bold text-uppercase fs-7">Dashboard</span></a>
                            </div>
                            <!--end:Menu content-->
                        </div>
                        <!--end:Menu item-->

                        @can('user_management_access')
                            <!--begin:User Management-->
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ request()->is('admin/permissions*') ? 'show' : '' }} {{ request()->is('admin/roles*') ? 'show' : '' }} {{ request()->is('admin/users*') ? 'show' : '' }} {{ request()->is('admin/audit-logs*') ? 'show' : '' }} {{ request()->is('admin/departments*') ? 'show' : '' }}">

                                <span class="menu-link">
                                    <span class="menu-icon">
                                        <i class="fa-solid fa-users"></i>
                                    </span>
                                    <span class="menu-title">User Management</span>
                                    <span class="menu-arrow"></span>
                                </span>

                                <div class="menu-sub menu-sub-accordion">
                                    @can('permission_access')
                                        <div class="menu-item">
                                            <a class="menu-link {{ request()->is('admin/permissions') || request()->is('admin/permissions/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.permissions.index') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">{{ trans('cruds.permission.title') }}</span>
                                            </a>
                                        </div>
                                    @endcan

                                    @can('role_access')
                                        <div class="menu-item">
                                            <a class="menu-link {{ request()->is('admin/roles') || request()->is('admin/roles/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.roles.index') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">{{ trans('cruds.role.title') }}</span>
                                            </a>
                                        </div>
                                    @endcan

                                    @can('user_access')
                                        <div class="menu-item">
                                            <a class="menu-link {{ request()->is('admin/users') || request()->is('admin/users/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.users.index') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">{{ trans('cruds.user.title') }}</span>
                                            </a>
                                        </div>
                                    @endcan
                                </div>
                            </div>
                            <!--end:User Management-->
                        @endcan
                        
                        <!--begin:Order-->
                        @can('page_access')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('admin/pages') || request()->is('admin/pages/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.pages.index') }}">
                                    <span class="menu-icon">
                                        <i class="fa-solid fa-file-lines"></i>
                                    </span>
                                    <span class="menu-title">Pages</span>
                                </a>
                            </div>
                        @endcan
                        <!--end:Query-->

                        <!--begin:Query-->
                        @can('contact_message_access')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('admin/contact-messages') || request()->is('admin/contact-messages/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.contact-messages.index') }}">
                                    <span class="menu-icon">
                                        <i class="fa-solid fa-envelope"></i>
                                    </span>
                                    <span class="menu-title">Contact Messages</span>
                                </a>
                            </div>
                        @endcan
                        <!--end:Query-->

                        @can('setting_access')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('admin/settings') || request()->is('admin/settings/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.settings.index') }}">
                                    <span class="menu-icon">
                                        <i class="fa-solid fa-gear"></i>
                                    </span>
                                    <span class="menu-title">Settings</span>
                                </a>
                            </div>

                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('admin/galleries') || request()->is('admin/galleries/*') ? 'active' : '' }}" target="_self" href="{{ route('admin.galleries.index','1') }}">
                                    <span class="menu-icon">
                                        <i class="fa-solid fa-gear"></i>
                                    </span>
                                    <span class="menu-title">Media Library</span>
                                </a>
                            </div>
                        @endcan
                        

                    </div>
                    <!--end::Menu-->
                </div>
                <!--end::Scroll wrapper-->
            </div>
            <!--end::Menu wrapper-->
        </div>
        <!--end::sidebar menu-->
</div>

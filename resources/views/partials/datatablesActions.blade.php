@if ($crudRoutePart == 'contact-messages')
    @can($viewGate)
        <a class="btn btn-icon btn-active-light-primary w-30px h-30px" href="{{ route('admin.' . $crudRoutePart . '.show', $row->id) }}">
            <span>
                <i class="fa-solid fa-eye text-white "></i>
            </span>
        </a>
    @endcan
@elseif($crudRoutePart == 'pages')
    {{-- @can($viewGate)
        <a class="btn btn-icon btn-active-light-primary w-30px h-30px" href="{{ route('admin.' . $crudRoutePart . '.show', $row->id) }}">
            <span>
                <i class="fa-solid fa-eye text-white"></i>
            </span>
        </a>
    @endcan --}}
    @can($editGate)
        <a class="btn btn-icon btn-active-light-primary w-30px h-30px" href="{{ route('admin.' . $crudRoutePart . '.edit', $row->id) }}">
            <span>
                <i class="fa-solid fa-pencil text-white"></i>
            </span>
        </a>

        
    @endcan

@else
    @can($viewGate)
        <a class="btn btn-icon btn-active-light-primary w-30px h-30px" href="{{ route('admin.' . $crudRoutePart . '.show', $row->id) }}">
            <span>
                <i class="fa-solid fa-eye text-white"></i>
            </span>
        </a>
    @endcan
    @can($editGate)
        <a class="btn btn-icon btn-active-light-primary w-30px h-30px" href="{{ route('admin.' . $crudRoutePart . '.edit', $row->id) }}">
            <span>
                <i class="fa-solid fa-pencil text-white"></i>
            </span>
        </a>
    @endcan

    @can($deleteGate)
        <form action="{{ route('admin.' . $crudRoutePart . '.destroy', $row->id) }}" method="POST" onsubmit="return confirm('{{ trans('global.areYouSure') }}');" style="display: inline-block;">
            <input type="hidden" name="_method" value="DELETE">
            <input type="hidden" name="_token" value="{{ csrf_token() }}">
            <button type="submit" class="btn btn-icon btn-active-light-primary w-30px h-30px me-3">
                <span>
                    <i class="fa-solid fa-trash text-white"></i>
                </span>
            </button>
        </form>
    @endcan


    {{-- <div class="btn-group table_drop">
        <button class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
            Actions <i class="ki-duotone ki-down fs-5 ms-1"></i>
        </button>
        <ul class="dropdown-menu p-2" aria-labelledby="dropdownMenuButton1">
            <li>
                @can($viewGate)
                    <a class="dropdown-item menu-link py-1" href="{{ route('admin.' . $crudRoutePart . '.show', $row->id) }}">
                        {{ trans('global.view') }}
                    </a>
                @endcan
            </li>
            <li>
                @can($editGate)
                    <a class="dropdown-item menu-link py-1" href="{{ route('admin.' . $crudRoutePart . '.edit', $row->id) }}">
                        {{ trans('global.edit') }}
                    </a>
                @endcan
            </li>
            <li>
                @can($deleteGate)
                    <form action="{{ route('admin.' . $crudRoutePart . '.destroy', $row->id) }}" method="POST" onsubmit="return confirm('{{ trans('global.areYouSure') }}');" style="display: inline-block;">
                        <input type="hidden" name="_method" value="DELETE">
                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                        <input type="submit" class="dropdown-item menu-link py-1" value="{{ trans('global.delete') }}">
                    </form>
                @endcan
            </li>
        </ul>
    </div> --}}
@endif

<?php

Route::group(['prefix' => 'v1', 'as' => 'api.', 'namespace' => 'Api\V1\Admin', 'middleware' => ['auth:sanctum']], function () {
    // Section
    Route::post('sections/media', 'SectionApiController@storeMedia')->name('sections.storeMedia');
    Route::apiResource('sections', 'SectionApiController');

    // Section Item
    Route::post('section-items/media', 'SectionItemApiController@storeMedia')->name('section-items.storeMedia');
    Route::apiResource('section-items', 'SectionItemApiController');

    // Contact Message
    Route::apiResource('contact-messages', 'ContactMessageApiController');
});

<?php

Route::get('/', 'Frontend\HomeController@index')->name('home');
Route::get('/missions', 'Frontend\HomeController@missions')->name('missions');
Route::get('/bureaux', 'Frontend\HomeController@bureaux')->name('bureaux');
Route::get('/localisations', 'Frontend\HomeController@localisations')->name('localisations');
Route::get('/comores', 'Frontend\HomeController@comores')->name('comores');
Route::get('/reunion', 'Frontend\HomeController@reunion')->name('reunion');
Route::get('/caledonie', 'Frontend\HomeController@caledonie')->name('caledonie');
Route::get('/madagascar', 'Frontend\HomeController@madagascar')->name('madagascar');
Route::get('/maurice', 'Frontend\HomeController@maurice')->name('maurice');
Route::get('/introduction', 'Frontend\HomeController@introduction')->name('introduction');
Route::get('/professionnels', 'Frontend\HomeController@professionnels')->name('professionnels');
Route::get('/assurances', 'Frontend\HomeController@assurances')->name('assurances');
Route::post('/contact', 'Frontend\HomeController@contactStore')->name('contact.save');
Route::get('/change-language/{lang}', 'Frontend\HomeController@changeLanguage')->name('change-language');


Auth::routes(['register' => false]);

Route::group(['prefix' => 'admin', 'as' => 'admin.', 'namespace' => 'Admin', 'middleware' => ['auth', 'admin']], function () {
    Route::get('/', 'HomeController@index')->name('home');
    // Permissions
    Route::delete('permissions/destroy', 'PermissionsController@massDestroy')->name('permissions.massDestroy');
    Route::post('permissions/parse-csv-import', 'PermissionsController@parseCsvImport')->name('permissions.parseCsvImport');
    Route::post('permissions/process-csv-import', 'PermissionsController@processCsvImport')->name('permissions.processCsvImport');
    Route::resource('permissions', 'PermissionsController');

    // Roles
    Route::delete('roles/destroy', 'RolesController@massDestroy')->name('roles.massDestroy');
    Route::post('roles/parse-csv-import', 'RolesController@parseCsvImport')->name('roles.parseCsvImport');
    Route::post('roles/process-csv-import', 'RolesController@processCsvImport')->name('roles.processCsvImport');
    Route::resource('roles', 'RolesController');

    // Users
    Route::delete('users/destroy', 'UsersController@massDestroy')->name('users.massDestroy');
    Route::post('users/parse-csv-import', 'UsersController@parseCsvImport')->name('users.parseCsvImport');
    Route::post('users/process-csv-import', 'UsersController@processCsvImport')->name('users.processCsvImport');
    Route::resource('users', 'UsersController');

    // Page
    Route::delete('pages/destroy', 'PageController@massDestroy')->name('pages.massDestroy');
    Route::post('pages/parse-csv-import', 'PageController@parseCsvImport')->name('pages.parseCsvImport');
    Route::post('pages/process-csv-import', 'PageController@processCsvImport')->name('pages.processCsvImport');
    Route::post('pages/upload-temp', 'PageController@uploadTemp')->name('pages.uploadTemp');
    Route::post('pages/drawer-form', 'PageController@drawerForm')->name('pages.drawerForm');
    Route::resource('pages', 'PageController');
    Route::get('pages/{page}/{language}/{section}', 'PageController@show')->name('pages.show'); // index

    // editor
    // Route::get('editors-index/{language}/{section}', 'PageController@editorIndex')->name('editors.index');
    // Route::put('editors-index-update', 'PageController@editorIndexUpdate')->name('editors.index.update');

    // Route::get('editors-missions/{language}/{section}', 'PageController@editorMissions')->name('editors.missions');
    // Route::put('editors-missions-update', 'PageController@editorMissionsUpdate')->name('editors.missions.update');

    Route::get('editors/{pageId}/{language}/{section}', 'PageController@editor')->name('editors.show');
    Route::put('editors-index-update', 'PageController@editorIndexUpdate')->name('editors.index.update');
    Route::put('editors-missions-update', 'PageController@editorMissionsUpdate')->name('editors.missions.update');
    Route::put('editors-bureaux-update', 'PageController@editorBureauxUpdate')->name('editors.bureaux.update');
    Route::put('editors-localisations-update', 'PageController@editorLocalisationsUpdate')->name('editors.localisations.update');
    Route::put('editors-introduction-update', 'PageController@editorIntroductionUpdate')->name('editors.introduction.update');
    Route::put('editors-professionnels-update', 'PageController@editorProfessionnelsUpdate')->name('editors.professionnels.update');
    Route::put('editors-assurances-update', 'PageController@editorAssurancesUpdate')->name('editors.assurances.update');
    Route::put('editors-comores-update', 'PageController@editorComoresUpdate')->name('editors.comores.update');
    Route::put('editors-reunion-update', 'PageController@editorReunionUpdate')->name('editors.reunion.update');
    Route::put('editors-caledonie-update', 'PageController@editorCaledonieUpdate')->name('editors.caledonie.update');
    Route::put('editors-madagascar-update', 'PageController@editorMadagascarUpdate')->name('editors.madagascar.update');
    Route::put('editors-maurice-update', 'PageController@editorMauriceUpdate')->name('editors.maurice.update');



    // Settings
    Route::delete('settings/destroy', 'SettingsController@massDestroy')->name('settings.massDestroy');
    Route::post('settings/parse-csv-import', 'SettingsController@parseCsvImport')->name('settings.parseCsvImport');
    Route::post('settings/process-csv-import', 'SettingsController@processCsvImport')->name('settings.processCsvImport');
    Route::resource('settings', 'SettingsController');

    // Seo
    Route::delete('seos/destroy', 'SeoController@massDestroy')->name('seos.massDestroy');
    Route::post('seos/media', 'SeoController@storeMedia')->name('seos.storeMedia');
    Route::post('seos/ckmedia', 'SeoController@storeCKEditorImages')->name('seos.storeCKEditorImages');
    Route::post('seos/parse-csv-import', 'SeoController@parseCsvImport')->name('seos.parseCsvImport');
    Route::post('seos/process-csv-import', 'SeoController@processCsvImport')->name('seos.processCsvImport');
    Route::resource('seos', 'SeoController');

    // Section
    // Route::delete('sections/destroy', 'SectionController@massDestroy')->name('sections.massDestroy');
    // Route::post('sections/media', 'SectionController@storeMedia')->name('sections.storeMedia');
    // Route::post('sections/ckmedia', 'SectionController@storeCKEditorImages')->name('sections.storeCKEditorImages');
    // Route::post('sections/parse-csv-import', 'SectionController@parseCsvImport')->name('sections.parseCsvImport');
    // Route::post('sections/process-csv-import', 'SectionController@processCsvImport')->name('sections.processCsvImport');
    // Route::resource('sections', 'SectionController');

    // Section Item
    // Route::delete('section-items/destroy', 'SectionItemController@massDestroy')->name('section-items.massDestroy');
    // Route::post('section-items/media', 'SectionItemController@storeMedia')->name('section-items.storeMedia');
    // Route::post('section-items/ckmedia', 'SectionItemController@storeCKEditorImages')->name('section-items.storeCKEditorImages');
    // Route::post('section-items/parse-csv-import', 'SectionItemController@parseCsvImport')->name('section-items.parseCsvImport');
    // Route::post('section-items/process-csv-import', 'SectionItemController@processCsvImport')->name('section-items.processCsvImport');
    // Route::resource('section-items', 'SectionItemController');

    // Contact Message
    Route::delete('contact-messages/destroy', 'ContactMessageController@massDestroy')->name('contact-messages.massDestroy');
    Route::post('contact-messages/parse-csv-import', 'ContactMessageController@parseCsvImport')->name('contact-messages.parseCsvImport');
    Route::post('contact-messages/process-csv-import', 'ContactMessageController@processCsvImport')->name('contact-messages.processCsvImport');
    Route::resource('contact-messages', 'ContactMessageController');

    // Gallery
    Route::get('galleries/{id}', 'GalleryController@index')->name('galleries.index');
    Route::post('galleries/{id}/upload', 'GalleryController@upload')->name('galleries.upload');

});
Route::group(['prefix' => 'profile', 'as' => 'profile.', 'namespace' => 'Auth', 'middleware' => ['auth']], function () {
    // Change password
    if (file_exists(app_path('Http/Controllers/Auth/ChangePasswordController.php'))) {
        Route::get('password', 'ChangePasswordController@edit')->name('password.edit');
        Route::post('password', 'ChangePasswordController@update')->name('password.update');
        Route::post('profile', 'ChangePasswordController@updateProfile')->name('password.updateProfile');
        Route::post('profile/destroy', 'ChangePasswordController@destroy')->name('password.destroyProfile');
    }
});

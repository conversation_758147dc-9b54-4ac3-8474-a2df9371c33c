<?php

namespace Tests\Browser;

use Illuminate\Foundation\Testing\DatabaseMigrations;
use Lara<PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class SectionItemTest extends DuskTestCase
{
    public function testIndex()
    {
        $admin = App\Models\User::find(1);
        $this->browse(function (Browser $browser) use ($admin) {
            $browser->loginAs($admin);
            $browser->visit(route('admin.sectionitem.index'));
            $browser->assertRouteIs('admin.sectionitem.index');
        });
    }
}

<?php

namespace Tests\Browser;

use Illuminate\Foundation\Testing\DatabaseMigrations;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class SeoTest extends DuskTestCase
{
    public function testIndex()
    {
        $admin = App\Models\User::find(1);
        $this->browse(function (Browser $browser) use ($admin) {
            $browser->loginAs($admin);
            $browser->visit(route('admin.seo.index'));
            $browser->assertRouteIs('admin.seo.index');
        });
    }
}
